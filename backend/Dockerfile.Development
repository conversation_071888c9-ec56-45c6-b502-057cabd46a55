FROM python:3.12-slim

RUN apt-get update && \
	apt-get install -y --no-install-recommends gcc libpq-dev python3-dev curl && \
	rm -rf /var/lib/apt/lists/*

ENV PYTHONUNBUFFERED=1

COPY --from=ghcr.io/astral-sh/uv:0.7.13 /uv /uvx /bin/

ENV UV_COMPILE_BYTE=1

ENV UV_LINK_MODE=copy

# Change the working directory to the `app` directory
WORKDIR /app

ENV PATH="/app/.venv/bin:$PATH"

COPY ./pyproject.toml ./uv.lock ./.python-version /app/

# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
	--mount=type=bind,source=uv.lock,target=uv.lock \
	--mount=type=bind,source=pyproject.toml,target=pyproject.toml \
	uv sync --frozen --no-install-project --no-dev

COPY . /app

# Sync the project
RUN --mount=type=cache,target=/root/.cache/uv \
	uv sync --frozen --no-dev

CMD ["fastapi", "dev", "main.py", "--host", "0.0.0.0"]
