from fastapi import APIRouter
from fastapi import APIRouter, status
from fastapi_pagination import Page
from src.modules.users.users_schema import UserResponse
from src.modules.users import users_controller as controller


router = APIRouter(tags=["users"])

router.add_api_route(
    path="",
    endpoint=controller.index,
    name='Get and Search For Users',
    methods=["GET"],
    status_code=status.HTTP_200_OK,
    response_model=Page[UserResponse]
)

router.add_api_route(
    path="",
    name='Create User',
    endpoint=controller.create,
    methods=["POST"],
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
    name="Edit User Attributes",
    path="/{id}",
    endpoint=controller.update,
    methods=["PUT"],
    response_model=UserResponse,
    status_code=status.HTTP_200_OK,
)

router.add_api_route(
    path="/{id}",
    endpoint=controller.delete,
    methods=["DELETE"],
    name='Delete A User',
    status_code=status.HTTP_204_NO_CONTENT,
)
