from uuid import UUID
from src.modules.auth.auth_schema import UserCreate
from src.core.exceptions.api import ApiException
from src.modules.users.users_schema import UserResponse, UserFilters, UserUpdate
from src.core.base.application_repository import ApplicationRepository
from src.config.db.models.user import User

class UsersService(ApplicationRepository):

    def __init__(self):
        super().__init__()

    def find_users(self, filters: UserFilters) -> list:
        """
          Find users based on filters.

          Args:
            filters (dict): Dictionary of filters to apply.

          Returns:
            list: List of users matching the criteria.
        """
        user_filters = ['first_name', 'middle_name', 'last_name', 'email', 'is_external']

        account_filters = ['handle', 'type', 'status']

        try:
            ufilters: dict = {k: v for k,v in filters if v is not None and k in user_filters}
            afilters: dict = {k: v for k, v in filters if v is not None and k in account_filters}

            query = self.db.query(User)
            if ufilters:
                for field in ["first_name", "middle_name", "last_name"]:
                    if field in ufilters:
                        filter_value = ufilters.pop(field)
                        query = query.filter(
                            getattr(User, field).like(f"{filter_value}%")
                        )

                query = query.filter_by(**ufilters)
            if afilters:
                query = query.join(User.account).filter_by(**afilters)
            return query
        except Exception as e:
            self.logger.error(f"Error finding users: {e}")
            raise e

    def update_user(self, user_id: UUID, updates: UserUpdate) -> UserResponse:
        try:                        
            user = self.db.query(User).filter_by(id=user_id).first()
            if user:
                for k, v in updates:
                    if v is not None: 
                      setattr(user, k, v)
            
            account = user.account
            if account:
              for k, v in updates:
                if v is not None:
                  setattr(account, k, v)
            else:
                raise ApiException('user not found')
            self.db.commit()
            return UserResponse.model_validate(user)
        except Exception as e:
            self.logger.error(f"Error updating user: {e}")
            raise e

    def delete_user(self, user_id) -> None:
      try:
          user = self.db.query(User).filter_by(id=user_id).first()
          if user.account.handle == 'root':
            return ApiException('Cannot delete superuser account')

          if user:
              self.db.delete(user)
              self.db.commit()
              return
          else:
              raise ApiException('user not found')
      except Exception as e:
          self.logger.error(f"Error deleting user: {e}")
          raise e