from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi_pagination.ext.sqlalchemy import paginate
from src.modules.auth.auth_schema import UserCreate
from src.modules.auth.auth_service import AuthService
from src.modules.users.users_schema import UserResponse, UserFilters, UserUpdate
from src.modules.users.users_service import UsersService

service = UsersService()
auth = AuthService()

def index(filters: UserFilters = Depends(UserFilters)) -> list[UserResponse]:
    try:
        return paginate(service.find_users(filters))
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

def create(user: UserCreate) -> UserResponse:
    try:
        return auth.register_user(user)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def update(id, updates: UserUpdate) -> UserResponse:
    try:
        return service.update_user(user_id=id, updates=updates)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def delete(user_id: UUID):
    try:
        return service.delete_user(user_id)
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
