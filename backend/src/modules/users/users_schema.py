from typing import Optional
from uuid import UUID
from pydantic import BaseModel, ConfigDict, EmailStr

from src.core.utils.common import super_to_optional
from src.config.db.models.base import Gender
from src.config.db.models.account import AccountStatus, AccountType
from src.core.shared_schema import BaseRequest


class AccountBaseDto(BaseModel):
  model_config = ConfigDict(from_attributes=True)
  
  handle: str
  type: AccountType
  status: AccountStatus

class UserBaseDto(BaseModel):
  model_config = ConfigDict(from_attributes=True)
  
  first_name: str
  middle_name: Optional[str]
  last_name: str
  email: EmailStr
  gender: Gender

class AccountResponse(AccountBaseDto):
  id: UUID

class UserResponse(UserBaseDto):
  id: UUID
  account: AccountResponse

class UserUpdateDto(UserBaseDto):
    handle: Optional[str] = None
    type: Optional[AccountType] = None
    is_external: Optional[bool] = None
    status: Optional[AccountStatus] = None

UserUpdate = super_to_optional(UserUpdateDto)

class UserFilters(BaseRequest):
    """Filters for users."""
    first_name: Optional[str] = None
    middle_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    handle: Optional[str] = None
    type: Optional[AccountType] = None
    is_external: Optional[bool] = None
    status: Optional[AccountStatus] = None
