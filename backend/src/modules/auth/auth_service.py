from typing import Dict

from fastapi import Depends
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer

from src.modules.users.users_schema import UserResponse
from src.config import settings
from src.config.db.models import Account, AccountStatus, AccountType, User
from src.core.base.application_repository import ApplicationRepository
from src.core.exceptions.api import ApiException
from src.core.logger import logger
from src.core.services.encryption_service import EncryptionService
from src.core.shared_schema import CurrentUser
from src.modules.auth.auth_schema import LoginResponse, UserCreate


class AuthService(ApplicationRepository):
	def __init__(self):
		super().__init__()
		self.oauth2_scheme = OAuth2PasswordBearer(tokenUrl="v1/auth/login")
		self.token_expiry = settings.ACCESS_TOKEN_EXPIRE_MINUTES
		self.encryption_service = EncryptionService()


	def _generate_auth_tokens(self, user: User) -> Dict[str, str]:
		token_data = {
			"sub": str(user.id),
			"email": user.email
		}

		return self.encryption_service.generate_tokens(
			user_id=user.id,
			additional_data=token_data
		)


	def get_current_user(self, token: str) -> (CurrentUser | None):
		"""
		Validate token and return current user
		"""
		is_valid, payload = self.encryption_service.verify_token(token)

		if not is_valid or payload.get("type") != "access":
			return None

		username = payload.get("sub")

		if not username:
			return None

		user = (self.db.query(User)
				.join(Account, User.account)
				.filter(Account.handle == username).first())

		if not user:
			return None

		if user.account.status != AccountStatus.ACTIVE:
			return None

		return CurrentUser(
			id=user.id,
			username=user.account.handle,
			role_id=user.user_role.role.id
		)


	def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
		"""
		Use a refresh token to generate a new access token
		"""
		is_valid, payload = self.encryption_service.verify_token(refresh_token)

		if not is_valid or payload.get("type") != "refresh":
			raise ApiException("Invalid refresh token")

		user_id:str = payload.get("sub")
		user = self.db.query(User).join(Account, User.account).where(User.id == user_id).first()

		if not user or user.account.status != AccountStatus.ACTIVE:
			raise ApiException("User was not found or inactive")

		token_data = {
			"sub": str(user.id),
			"email": user.email
		}
		access_token = self.encryption_service.create_access_token(token_data)

		return {
			"access_token": access_token,
			"token_type": "bearer"
		}


	async def get_current_user_dependency(self, token: str = Depends(OAuth2PasswordBearer(tokenUrl="v1/auth/login"))) -> User:
		"""
		Dependency that validates the token and returns the current user
		"""
		return await self.get_current_user(token)


	def initiate_password_reset(self, email: str) -> str:
		"""
		Create a password reset token and return it
		In a real app, you would send this via email
		"""
		user = self.db.query(User).where(User.email == email).first()

		if not user:
			return "If a user with this email exists, a password reset email has been sent."

		reset_token = self.encryption_service.generate_password_reset_token(email)

		#!TODO:  send email
		return reset_token


	def complete_password_reset(self, token: str, new_password: str) -> bool:
		"""
		Complete the password reset process by updating the user's password
		"""
		try:
			email = self.encryption_service.verify_password_reset_token(token)
			if not email:
				raise ApiException("Invalid or expired password reset token")

			user = self.db.query(User).where(User.email == email).first()

			if not user:
				raise ApiException("User not found")

			hashed_password = self.encryption_service.hash_password(new_password)
			user.hashed_password = hashed_password

			self.db.add(user)

			return True
		except Exception as e:
			logger.info(str(e))
			raise ApiException("Failed to complete password reset process")


	def authenticate_user(self, username: str, password: str) -> (LoginResponse | None):
		try:
			user = (self.db.query(User)
					.join(Account, User.account)
					.filter(User.email == username).first()
					)
			if not user or not self.encryption_service.verify_password(password, user.hashed_password):
				raise ApiException(f"Incorrect email or password")


			if not user.account.status != AccountStatus.ACTIVE.value:
				internal_user_message = "Failed to login, please ask your administrator"
				external_user_message = "Please verify your account first"
				raise ApiException(external_user_message if user.is_external else internal_user_message)

			return LoginResponse(
				first_name=user.first_name,
				last_name=user.last_name,
				username=user.account.handle,
				email=user.email,
				user_id=user.id,
				status="OK",
				created_at=user.created_at,
				updated_at=user.updated_at,
				auth=self._generate_auth_tokens(user)
			)
		except Exception as e:
			logger.info(e)
			raise ApiException(str(e))


	def register_user(self, payload: UserCreate) -> UserResponse:
		"""
		Register a new user in the database with hashed password
		"""
		try:
			existing_account = self.db.query(Account).filter(Account.handle == payload.username).one_or_none()

			if existing_account is not None:
				raise ApiException("Username already exists")

			existing_user = self.db.query(User).filter(User.email == payload.email).one_or_none()

			if existing_user is not None:
				raise ApiException("Email already registered")

			account = Account(
				type=AccountType.USER,
				status=AccountStatus.INACTIVE,
				handle=payload.username,
			)

			self.db.add(account)
			self.db.flush()

			hashed_password = self.encryption_service.hash_password(payload.password)

			user = User(
				first_name=payload.first_name,
				middle_name=payload.middle_name,
				last_name=payload.last_name,
				gender=payload.gender,
				email=str(payload.email),
				hashed_password=hashed_password,
				is_external=True
			)
			user.account_id = account.id

			self.db.add(user)   
			self.db.commit()
   
			# !TODO: Send account confirmation email

			return user
		except Exception as e:
			logger.info(str(e))
			raise ApiException("Failed to register user")
