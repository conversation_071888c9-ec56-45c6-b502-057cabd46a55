from fastapi import APIRouter, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON>wordBearer

from src.core.shared_schema import BaseResponse
import src.modules.auth.auth_schema as schema
import src.modules.auth.auth_controller as controller

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

router = APIRouter(tags=["auth"])

router.add_api_route(
    path="/register",
    endpoint=controller.register,
    methods=["POST"],
    response_model=schema.UserResponse,
    status_code=status.HTTP_201_CREATED
)

router.add_api_route(
    path="/login",
    endpoint=controller.login,
    methods=["POST"],
    response_model=BaseResponse[schema.LoginResponse],
	summary="Login to get access token",
	description="Authenticate with email and password to receive JWT tokens"
)

router.add_api_route(
    path="/me",
    endpoint=controller.get_current_user,
    methods=["GET"],
    response_model=schema.UserResponse
)

router.add_api_route(
    path="/request-password-reset",
    endpoint=controller.request_password_reset,
    methods=["POST"]
)

router.add_api_route(
    path="/reset-password",
    endpoint=controller.reset_password,
    methods=["POST"]
)