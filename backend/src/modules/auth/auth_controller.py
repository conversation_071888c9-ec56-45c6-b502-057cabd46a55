from fastapi import Depends, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordRequestForm

from src.core.exceptions.api import ApiException
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse
from src.modules.auth.auth_schema import (LoginResponse, PasswordReset,
                                          PasswordResetRequest, UserCreate,
                                          UserResponse)
from src.modules.auth.auth_service import AuthService

auth_service = AuthService()


def register(user_data: UserCreate) -> UserResponse:
    """
    Register a new user.

    Args:
        user_data: User registration data

    Returns:
        UserResponse: The newly created user
    """
    try:
        # Register the user and return the response
        user = auth_service.register_user(user_data)
        return user
    except ApiException as e:
        # Handle any exceptions
        raise bad_request([{"message": e.message}])


def login(form_data: OAuth2PasswordRequestForm = Depends()) -> BaseResponse[LoginResponse]:
    """
    Authenticate a user and return access tokens.

    Args:
        form_data: Form with username and password

    Returns:
        BaseResponse[LoginResponse]: User data with auth tokens
    """
    try:
        user = auth_service.authenticate_user(form_data.username, form_data.password)
        if not user:
            raise bad_request([{"message": "Invalid username or password"}])

        return BaseResponse(data=user)
    except ApiException as e:
        # Handle any exceptions
        raise bad_request([{"message": e.message}])


def get_current_user(
    token: str = Depends(auth_service.oauth2_scheme)
) -> UserResponse:
    """
    Get the current authenticated user.

    Args:
        token: JWT from Authorization header

    Returns:
        UserResponse: Current user data
    """
    try:
        # Get and return the current user
        return auth_service.get_current_user(token)
    except HTTPException:
        # Re-raise any HTTPExceptions from the service
        raise
    except Exception as e:
        # Handle any other exceptions
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}"
        )


def request_password_reset(
    reset_request: PasswordResetRequest,
) -> JSONResponse:
    """
    Request a password reset email.

    Args:
        reset_request: Request with user's email

    Returns:
        JSONResponse: Confirmation message
    """
    try:
        # Initiate the password reset
        auth_service.initiate_password_reset(reset_request.email)

        # Return a success message
        # Note: For security, we don't confirm if the email exists
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"detail": "Reset instructions sent if email exists"}
        )
    except Exception as e:
        # Log the error but don't expose it to the client
        print(f"Password reset error: {str(e)}")

        # Return the same message even if there was an error
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"detail": "Reset instructions sent if email exists"}
        )


def reset_password(
    reset_data: PasswordReset
) -> JSONResponse:
    """
    Complete the password reset process.

    Args:
        reset_data: Reset token and new password

    Returns:
        JSONResponse: Confirmation message
    """
    try:
        # Complete the password reset
        auth_service.complete_password_reset(reset_data.token, reset_data.new_password)

        # Return a success message
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"detail": "Password updated successfully"}
        )
    except HTTPException:
        # Re-raise any HTTPExceptions from the service
        raise
    except Exception as e:
        # Handle any other exceptions
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Password reset failed: {str(e)}"
        )
