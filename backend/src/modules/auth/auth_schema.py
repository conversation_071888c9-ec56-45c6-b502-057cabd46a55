from typing import Dict
from pydantic import UUID4, BaseModel, EmailStr
from datetime import datetime

from src.config.db.models.base import Gender

class UserBase(BaseModel):
	first_name: str
	last_name: str
	middle_name: str | None = None
	username: str
	gender: Gender
	phone: str | None = None
	email: EmailStr


class UserCreate(UserBase):
	password: str


class UserResponse(UserBase):
	user_id: UUID4
	status: str
	created_at: datetime
	updated_at: datetime
	class Config:
		from_attributes = True


class LoginResponse(UserResponse):
	auth: Dict[str, str]
	email: str

class LoginRequest(BaseModel):
	username: str
	password: str


class Token(BaseModel):
	access_token: str
	refresh_token: str
	token_type: str


class PasswordResetRequest(BaseModel):
	email: EmailStr


class PasswordReset(BaseModel):
	token: str
	new_password: str
