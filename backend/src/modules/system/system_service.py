import datetime
import os
import platform
import time
from typing import Dict, Optional

import psutil
from fastapi import Depends
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from src.config import settings
from src.config.db.database import get_db
from src.core.base.application_repository import ApplicationRepository
from src.modules.system.system_schema import (HealthCheckResponse,
                                              HealthStatus, ResourceMetrics,
                                              ServiceHealth, SystemInfo)


class SystemService(ApplicationRepository):
	"""Service for system health checks and monitoring"""

	# Store the application start time
	_start_time = datetime.datetime.now()

	def __init__(self):
		super().__init__()

	def get_system_info(self) -> SystemInfo:
		"""
		Get basic system information including version and uptime

		Returns:
			SystemInfo: Basic system information
		"""
		current_time = datetime.datetime.now()
		uptime_seconds = int((current_time - self._start_time).total_seconds())

		return SystemInfo(
			version=settings.APP_VERSION,
			environment=settings.ENVIRONMENT,
			start_time=self._start_time,
			uptime_seconds=uptime_seconds
		)

	def get_resource_metrics(self) -> ResourceMetrics:
		"""
		Collect system resource metrics like CPU, memory and disk usage

		Returns:
			ResourceMetrics: System resource metrics
		"""
		try:
			cpu_percent = psutil.cpu_percent(interval=0.1)

			memory = psutil.virtual_memory()
			memory_percent = memory.percent

			disk = psutil.disk_usage('/')
			disk_percent = disk.percent

			return ResourceMetrics(
				cpu_usage_percent=cpu_percent,
				memory_usage_percent=memory_percent,
				disk_usage_percent=disk_percent
			)
		except Exception:
			# If psutil fails, return None values
			return ResourceMetrics()

	def check_database_health(self) -> ServiceHealth:
		"""
		Check database connectivity and performances

		Returns:
			ServiceHealth: Database health status
		"""
		start_time = time.time()
		try:
			self.db.execute(text("SELECT 1"))

			return ServiceHealth(
				status=HealthStatus.HEALTHY,
				name="database",
				description="PostgreSQL Database",
				latency_ms=int((time.time() - start_time) * 1000),
				last_checked=datetime.datetime.now()
			)
		except SQLAlchemyError as e:
			return ServiceHealth(
				status=HealthStatus.UNHEALTHY,
				name="database",
				description="PostgreSQL Database",
				latency_ms=int((time.time() - start_time) * 1000),
				error=str(e),
				last_checked=datetime.datetime.now()
			)

	def check_external_services(self) -> Dict[str, ServiceHealth]:
		"""
		Check the health of external services the application depends on

		Returns:
			Dict[str, ServiceHealth]: Health status for each external service
		"""
		services = {}

		# Add any external service checks here
		# For example: email service, payment gateway, etc.

		services["email"] = ServiceHealth(
			status=HealthStatus.HEALTHY,
			name="email",
			description="Email Service",
			last_checked=datetime.datetime.now()
		)

		return services

	def get_health_status(self) -> HealthCheckResponse:
		"""
		Perform a comprehensive health check of all system components

		Returns:
			HealthCheckResponse: Complete health check response
		"""
		system_info = self.get_system_info()

		services = {}
		services["database"] = self.check_database_health()

		external_services = self.check_external_services()
		services.update(external_services)

		metrics = self.get_resource_metrics()

		overall_status = HealthStatus.HEALTHY
		for service in services.values():
			if service.status == HealthStatus.UNHEALTHY:
				overall_status = HealthStatus.UNHEALTHY
				break
			elif service.status == HealthStatus.DEGRADED:
				overall_status = HealthStatus.DEGRADED

		return HealthCheckResponse(
			status=overall_status,
			system=system_info,
			services=services,
			metrics=metrics
		)

