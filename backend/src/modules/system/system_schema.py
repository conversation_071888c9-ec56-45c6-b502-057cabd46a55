from enum import Enum
from typing import Dict, List, Optional
from datetime import datetime
from pydantic import UUID4, BaseModel, Field, HttpUrl

from src.core.shared_schema import BaseRequest


class HealthStatus(str, Enum):
    """Possible health status values"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class ServiceHealth(BaseModel):
    """Health status of an individual service component"""
    status: HealthStatus
    name: str
    description: Optional[str] = None
    latency_ms: Optional[int] = None
    error: Optional[str] = None
    last_checked: datetime = Field(default_factory=datetime.now)


class SystemInfo(BaseModel):
    """Basic system information"""
    version: str
    environment: str
    start_time: datetime
    uptime_seconds: int


class ResourceMetrics(BaseModel):
    """Basic resource metrics"""
    cpu_usage_percent: Optional[float] = None
    memory_usage_percent: Optional[float] = None
    disk_usage_percent: Optional[float] = None


class HealthCheckResponse(BaseModel):
    """Complete health check response"""
    status: HealthStatus
    timestamp: datetime = Field(default_factory=datetime.now)
    system: SystemInfo
    services: Dict[str, ServiceHealth]
    metrics: Optional[ResourceMetrics] = None
