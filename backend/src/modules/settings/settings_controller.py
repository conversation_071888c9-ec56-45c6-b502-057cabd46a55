from typing import List

from fastapi import Depends

from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, CurrentUser

from .settings_schema import (CountryDto, CountryFilter, DistrictDto,
                              DistrictFilter, LoadableItemDto,
                              LoadableItemFilter, LoadableItemRequest,
                              RegionDto, RegionFilter)
from .settings_service import SettingsService

settings_service = SettingsService()


def fetch_countries_handler(filter: CountryFilter = Depends(CountryFilter)):
	response = settings_service.retrieve_countries(filter)

	return BaseResponse[List[CountryDto]](
		data=response.data,
		total=response.total_count
	)

def fetch_districts_handler(filter: DistrictFilter = Depends(DistrictFilter)):
	response = settings_service.retrieve_districts(filter)

	return BaseResponse[List[DistrictDto]](
		data=response.data,
		total=response.total_count
	)


def fetch_regions_handler(filter: RegionFilter = Depends(RegionFilter)):
	response = settings_service.retrieve_regions(filter)

	return BaseResponse[List[RegionDto]](
		data=response.data,
		total=response.total_count
	)


def fetch_loadable_item_handler(
	filter: LoadableItemFilter = Depends(LoadableItemFilter),
	# user: CurrentUser = Depends(auth_guard.current_user)
):
	try:
		filter_result = settings_service.retrieve_loadable_items(filter)

		return BaseResponse[List[LoadableItemDto]](
			data=filter_result.data,
			total=filter_result.total_count
		)
	except ApiException as e:
		raise bad_request([{"message": e.message}])


def create_loadable_item_handler(
	data: LoadableItemRequest,
	user: CurrentUser = Depends(auth_guard.current_user)
):
	try:
		item = settings_service.add_loadable_item(user, data)

		return BaseResponse[LoadableItemDto](data=item)
	except ApiException as e:
		raise bad_request([{"message": e.message}])


def update_loadable_item_handler(
	data: LoadableItemRequest,
	user: CurrentUser = Depends(auth_guard.current_user)
):
	try:
		item = settings_service.update_loadable_item(user, data)

		return BaseResponse[LoadableItemDto](data=item)
	except ApiException as e:
		raise bad_request([{"message": e.message}])


def delete_loadable_item_handler(
	id: str,
	user: CurrentUser = Depends(auth_guard.current_user)
):
	try:
		deleted = settings_service.delete_loadable_item(user, id)

		return BaseResponse[bool](data=deleted)
	except ApiException as e:
		raise bad_request([{"message": e.message}])
	except ApiException as e:
		raise bad_request([{"message": e.message}])
