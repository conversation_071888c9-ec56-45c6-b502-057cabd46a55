from typing import List

from src.config.db.models import Country, LoadableItem
from src.config.db.models.district import District
from src.config.db.models.region import Region
from src.core.base.application_repository import ApplicationRepository
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser, FilterResponse
from src.core.utils import serializer

from .settings_schema import (CountryDto, CountryFilter, DistrictDto,
                              DistrictFilter, LoadableItemDto,
                              LoadableItemFilter, LoadableItemRequest,
                              RegionDto, RegionFilter)


class SettingsService(ApplicationRepository):
	def __init__(self):
		super().__init__()


	def retrieve_countries(self, filter: CountryFilter) -> FilterResponse[List[CountryDto]]:
		"""Retrieve countries based on the provided filter."""
		query = self.db.query(Country).order_by(Country.name.asc())

		if filter.name:
			query = query.filter(Country.name.ilike(f"%{filter.name}%"))

		total_count = query.count()

		if filter.paginate:
			query = query.offset((filter.page - 1) * filter.size).limit(filter.size)

		rows = query.all()
		countries = [serializer.to_country_dto(row) for row in rows]

		return FilterResponse[List[CountryDto]](data=countries, total_count=total_count)


	def retrieve_regions(self, filter: RegionFilter) -> FilterResponse[List[RegionDto]]:
		"""Retrieve countries based on the provided filter."""
		query = self.db.query(Region)

		if filter.name:
			query = query.filter(Region.name.ilike(f"%{filter.name}%"))

		total_count = query.count()
		rows = query.all()
		regions = [serializer.to_region_dto(row) for row in rows]

		return FilterResponse[List[RegionDto]](data=regions,total_count=total_count)


	def retrieve_districts(self, filter: DistrictFilter) -> FilterResponse[List[DistrictDto]]:
		"""Retrieve countries based on the provided filter."""
		query = self.db.query(District).join(Region, District.region)

		if filter.name:
			query = query.filter(District.name.ilike(f"%{filter.name}%"))

		if filter.region_id:
			query = query.filter(District.region_id == filter.region_id)

		total_count = query.count()

		if filter.paginate:
			query = query.offset((filter.page - 1) * filter.size).limit(filter.size)

		rows = query.all()
		data = [serializer.to_district_dto(row) for row in rows]

		return FilterResponse[List[DistrictDto]](data=data, total_count=total_count)


	def retrieve_loadable_items(
			self,
			filter: LoadableItemFilter
	) -> FilterResponse[List[LoadableItemDto]]:
		"""Retrieve loadable items based on the provided filter."""
		query = self.db.query(LoadableItem)

		if filter.type:
			query = query.filter(LoadableItem.type == filter.type)

		if filter.display_value:
			query = query.filter(LoadableItem.display_value.ilike(f"%{filter.display_value}%"))

		if filter.code:
			query = query.filter(LoadableItem.code.ilike(f"%{filter.code}%"))

		total_count = query.count()

		if filter.paginate:
			query = query.offset((filter.page - 1) * filter.size).limit(filter.size)

		rows = query.all()
		items = [serializer.to_loadable_item_dto(row) for row in rows]

		return FilterResponse[List[LoadableItemDto]](
			total_count=total_count,
			data=items
		)


	def add_loadable_item(self, user: CurrentUser, data: LoadableItemRequest) -> LoadableItemDto:
		"""Add a new loadable item."""
		try:
			item = self.db.query(LoadableItem).filter(LoadableItem.code == data.code).first()
			if item is not None:
				raise ApiException(f"Loadable item with code {data.code} already exists")

			item = self.db.query(LoadableItem).filter(
				(LoadableItem.type == data.type) &
				(LoadableItem.display_value == data.display_value)
			).first()
			if item is not None:
				raise ApiException("Loadable item with same name and type already exists")

			loadable_item = LoadableItem(
				type=data.type.upper(),
				code=data.code.upper(),
				display_value=data.display_value,
				description=data.description,
				created_by=user.id
			)
			self.db.add(loadable_item)
			self.db.commit()
			self.db.refresh(loadable_item)

			return serializer.to_loadable_item_dto(loadable_item)
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException(f"Failed to add loadable item: {str(e)}")


	def update_loadable_item(
			self,
			user: CurrentUser,
			data: LoadableItemRequest
	) -> LoadableItemDto:
		"""Update an existing loadable item."""
		try:
			loadable_item = self.db.query(LoadableItem).filter(
				LoadableItem.loadable_item_id == data.loadable_item_id
			).first()

			if loadable_item is None:
				raise ApiException("Could not find a loadable item to update")

			loadable_item.code = data.code.upper()
			loadable_item.type = data.type.upper()
			loadable_item.description = data.description
			loadable_item.display_value = data.display_value
			loadable_item.updated_by = user.user_id

			self.db.commit()
			self.db.refresh(loadable_item)

			return serializer.to_loadable_item_dto(loadable_item)
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException(f"Failed to update loadable item: {str(e)}")


	def delete_loadable_item(self, user: CurrentUser, loadable_item_id: str) -> bool:
		"""Delete (void) a loadable item."""
		try:
			loadable_item = self.db.query(LoadableItem).filter(
				LoadableItem.loadable_item_id == loadable_item_id
			).first()

			if loadable_item is None:
				raise ApiException("Could not find a loadable item to delete")

			loadable_item.voided_by = user.user_id
			loadable_item.voided = True

			self.db.commit()
			self.db.refresh(loadable_item)
			return True
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException("Failed to delete loadable item")
			raise ApiException("Failed to delete loadable item")
			raise ApiException("Failed to delete loadable item")
