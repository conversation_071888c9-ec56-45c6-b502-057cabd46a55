from datetime import datetime
from typing import Optional

from pydantic import UUID4, BaseModel, Field

from src.core.shared_schema import BaseRequest


class CountryFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Filter by country name")

class RegionFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Filter by country name")

class DistrictFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Filter by country name")
	region_id: Optional[UUID4] = Field(default=None, description="Filter by region id")

class LoadableItemFilter(BaseRequest):
	code: Optional[str] = Field(default=None, description="Filter by loadable item name")
	type: Optional[str] = Field(default=None, description="Filter by loadable item type")
	display_value:  Optional[str] = Field(
		default=None,
		description="Filter by loadable item name"
	)


class RegionDto(BaseModel):
	id: UUID4
	name: str
	code: str
	created_at: datetime
	updated_at: datetime

class DistrictDto(BaseModel):
	id: UUID4
	name: str
	code: str
	region_id: UUID4
	created_at: datetime
	updated_at: datetime
	region_name: str

class LoadableItemDto(BaseModel):
	id: UUID4
	type: str
	code: str
	display_value: str
	description: str
	created_at: datetime
	updated_at: datetime

class LoadableItemRequest(BaseModel):
	id: Optional[str] = Field(default=None, description="Id")
	type: str
	code: str
	display_value: str
	description: Optional[str] = Field(default=None, description="Description")

class CountryFilter(BaseRequest):
	name: Optional[str] = Field(default=None, description="Name of the country")

class CountryDto(BaseModel):
	id: UUID4
	name: str
	dial_code: str
	short_code: str
	flag: str
