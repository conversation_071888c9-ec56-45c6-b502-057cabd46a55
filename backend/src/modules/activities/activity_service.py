from typing import List
from src.config.db.models import Activity
from src.core.base.application_repository import ApplicationRepository
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser, FilterResponse
from src.core.utils import serializer
from uuid import UUID

from .activity_schema import ActivityDto, ActivityFilter, ActivityRequest


class ActivityService(ApplicationRepository):
    def __init__(self):
        super().__init__()

    def retrieve_activities(self, filter: ActivityFilter) -> FilterResponse[List[ActivityDto]]:
        query = self.db.query(Activity).filter(Activity.voided == False).order_by(Activity.start_date.desc())

        if filter.title:
            query = query.filter(Activity.title.ilike(f"%{filter.title}%"))

        total = query.count()

        if filter.paginate:
            query = query.offset((filter.page - 1) * filter.size).limit(filter.size)

        rows = query.all()
        data = [serializer.to_activity_dto(row) for row in rows]

        return FilterResponse(total_count=total, data=data)

    def get_activity_by_id(self, activity_id: UUID) -> ActivityDto:
        try:
            activity = (
                self.db.query(Activity)
                .filter(Activity.id == activity_id, Activity.voided == False)
                .first()
            )

            if not activity:
                raise ApiException("Activity not found")

            return serializer.to_activity_dto(activity)
        except Exception as e:
            raise ApiException(f"Failed to retrieve activity: {str(e)}")


    def create_activity(self, user: CurrentUser, data: ActivityRequest) -> ActivityDto:
        try:
          activity_data = data.model_dump()
          activity = Activity(**activity_data, created_by=user.id)
          self.db.add(activity)
          self.db.commit()
          self.db.refresh(activity)
          return serializer.to_activity_dto(activity)
        except Exception as e:
          self.db.rollback()
          raise ApiException(f"Failed to create activity: {str(e)}")
    

    def update_activity(self, user: CurrentUser, id: UUID, data: ActivityRequest) -> ActivityDto:
        try:
            activity = self.db.query(Activity).filter(Activity.id == id, Activity.voided == False).first()

            if not activity:
                raise ApiException("Activity not found")

            for key, value in data.model_dump().items():
                setattr(activity, key, value)

            activity.updated_by = user.id
            self.db.commit()
            self.db.refresh(activity)
            return serializer.to_activity_dto(activity)

        except Exception as e:
            self.db.rollback()
            raise ApiException(f"Failed to update activity: {str(e)}")


    def delete_activity(self, user: CurrentUser, activity_id: UUID) -> bool:
        try:
            activity = self.db.query(Activity).filter(Activity.id == activity_id, Activity.voided == False).first()
            if not activity:
                raise ApiException("Activity not found")

            activity.voided_by = user.id
            activity.voided = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise ApiException(f"Failed to delete activity: {str(e)}")
