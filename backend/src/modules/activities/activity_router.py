from typing import List
from fastapi import APIRouter

import src.modules.activities.activity_controller as controller
import src.modules.activities.activity_schema as schema
from src.core.shared_schema import BaseResponse

router = APIRouter(tags=["activity"])

router.add_api_route(
    path="",
    endpoint=controller.fetch_activities_handler,
    response_model=BaseResponse[List[schema.ActivityDto]],
    methods=["GET"],
    description="Retrieve list of activities"
)

router.add_api_route(
    path="/{id}",
    endpoint=controller.get_activity_handler,
    response_model=BaseResponse[schema.ActivityDto],
    methods=["GET"],
    description="Retrieve a single activity by ID"
)

router.add_api_route(
    path="",
    endpoint=controller.create_activity_handler,
    response_model=BaseResponse[schema.ActivityDto],
    methods=["POST"],
    description="Create a new activity"
)

router.add_api_route(
    path="/{id}",
    endpoint=controller.update_activity_handler,
    response_model=BaseResponse[schema.ActivityDto],
    methods=["PUT"],
    description="Update an existing activity"
)

router.add_api_route(
    path="/{id}",
    endpoint=controller.delete_activity_handler,
    response_model=BaseResponse[bool],
    methods=["DELETE"],
    description="Delete an activity"
)
