from typing import List
from uuid import UUID

from fastapi import Depends, Body, Path
from src.core.guards.auth_guard import auth_guard

from src.core.exceptions.api import ApiException
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, CurrentUser

from .activity_service import ActivityService
from .activity_schema import ActivityDto, ActivityFilter, ActivityRequest


activity_service = ActivityService()

def fetch_activities_handler(
    user: CurrentUser = Depends(auth_guard.current_user),
    filter: ActivityFilter = Depends(ActivityFilter),
    
):
    try:
        result = activity_service.retrieve_activities(filter)
        return BaseResponse[List[ActivityDto]](data=result.data, total=result.total_count)
    except ApiException as e:
        raise bad_request([{"message": e.message}])


def get_activity_handler(
    id: UUID = Path(..., description="ID of the activity to fetch"),
    user: CurrentUser = Depends(auth_guard.current_user),
):
    try:
        result = activity_service.get_activity_by_id(id)
        return BaseResponse[ActivityDto](data=result)
    except ApiException as e:
        raise bad_request([{"message": e.message}])
    

def create_activity_handler(
    data: ActivityRequest,
    user: CurrentUser = Depends(auth_guard.current_user),
):
    try:
        result = activity_service.create_activity(user, data)
        return BaseResponse[ActivityDto](data=result)
    except ApiException as e:
        raise bad_request([{"message": e.message}])


def update_activity_handler(
    id: UUID = Path(..., description="ID of the activity to update"),
    data: ActivityRequest = Body(...),
    user: CurrentUser = Depends(auth_guard.current_user),
):
    try:
        result = activity_service.update_activity(user, id, data)
        return BaseResponse[ActivityDto](data=result)
    except ApiException as e:
        raise bad_request([{"message": e.message}])


def delete_activity_handler(
    id: UUID = Path(..., description="ID of the activity to delete"),
    user: CurrentUser = Depends(auth_guard.current_user),
):
    try:
        result = activity_service.delete_activity(user, id)
        return BaseResponse[bool](data=result)
    except ApiException as e:
        raise bad_request([{"message": e.message}])
