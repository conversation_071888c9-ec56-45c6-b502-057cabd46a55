from typing import List

from fastapi import Depends

from src.core.dtos.organization_dtos import OrganizationDto
from src.core.exceptions.api import ApiException
from src.core.guards import auth_guard
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, CurrentUser

from .organization_schema import OrganizationFilter, OrganizationRequest
from .organization_service import OrganizationService

organization_service = OrganizationService()

def create_organization_handler(
	body: OrganizationRequest,
	user: CurrentUser = Depends(auth_guard.CurrentUser)
) -> BaseResponse[OrganizationDto]:
	try:
		organization = organization_service.create_organization(user, body)
		return BaseResponse[OrganizationDto](data=organization)
	except ApiException as e:
		raise bad_request([{"message": e.message}])

def fetch_organizations_handler(
	filter = Depends(OrganizationFilter)
) -> BaseResponse[List[OrganizationDto]]:
	try:
		response = organization_service.retrieve_organizations(filter)
		return BaseResponse[List[OrganizationDto]](data=response.data, total=response.total_count)
	except ApiException as e:
		raise bad_request([{"message": e.message}])
