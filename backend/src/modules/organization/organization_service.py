import secrets
import string
from datetime import datetime
from typing import List, Optional

from sqlalchemy.exc import IntegrityError

from src.config.db.models import Organization
from src.config.db.models.account import Account
from src.config.db.models.organization import OrganizationStatus
from src.core.base.application_repository import ApplicationRepository
from src.core.dtos.organization_dtos import OrganizationDto
from src.core.exceptions.api import ApiException
from src.core.logger import logger
from src.core.shared_schema import CurrentUser, FilterResponse
from src.core.utils import serializer

from .organization_schema import OrganizationFilter, OrganizationRequest


class OrganizationService(ApplicationRepository):
	def __init__(self):
		super().__init__()

	@staticmethod
	def _generate_registration_number() -> str:
		"""Generate a unique registration number for the organization."""
		# Generate a random alphanumeric string
		prefix = "ORG"
		random_part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
		return f"{prefix}{random_part}"

	def _validate_organization_data(self, data: OrganizationRequest) -> None:
		"""Validate organization data before creation."""
		existing_org = self.db.query(Organization).filter(
			Organization.name == data.name
		).first()

		if existing_org:
			raise ApiException("Organization with this name already exists")

		if data.annual_income < 0:
			raise ApiException("Annual income cannot be negative")


	def retrieve_organizations(self, filter_params: OrganizationFilter) -> FilterResponse[List[OrganizationDto]]:
		"""Retrieve organizations based on filter criteria."""
		try:
			query = self.db.query(Organization).join(Account, Organization.account)

			if filter_params.abbreviation:
				query = query.filter(Organization.abbreviation.ilike(f"%{filter_params.abbreviation}%"))

			if filter_params.district_id:
				query = query.filter(Organization.district_id == filter_params.district_id)

			if filter_params.name:
				query = query.filter(Organization.name.ilike(f"%{filter_params.name}%"))

			if filter_params.registration_number:
				query = query.filter(Organization.registration_number.ilike(f"%{filter_params.registration_number}%"))

			if filter_params.registration_type_id:
				query = query.filter(Organization.registration_type_id == filter_params.registration_type_id)

			if filter_params.status:
				query = query.filter(Organization.status == filter_params.status)

			total_count = query.count()

			if filter_params.paginate:
				offset = (filter_params.page - 1) * filter_params.size
				query = query.offset(offset).limit(filter_params.size)

			rows = query.all()
			items = [serializer.to_organization_dto(row) for row in rows]

			return FilterResponse[List[OrganizationDto]](
				total_count=total_count,
				data=items
			)

		except Exception as e:
			logger.error(f"Failed to retrieve organizations: {str(e)}")
			raise ApiException("Failed to retrieve organizations")

	def create_organization(self, user: CurrentUser, data: OrganizationRequest) -> OrganizationDto:
		"""Create a new organization."""
		try:
			self._validate_organization_data(data)
			registration_number = self._generate_registration_number()

			while self.db.query(Organization).filter(
				Organization.registration_number == registration_number
			).first():
				registration_number = self._generate_registration_number()

			financial_start_month = self._parse_date_string(data.financial_start_month)
			financial_end_month = self._parse_date_string(data.financial_end_month)

			organization = Organization(
				name=data.name,
				abbreviation=data.abbreviation,
				organization_type_id=data.organization_type_id,
				registration_number=registration_number,
				district_id=data.district_id,
				financial_start_month=financial_start_month,
				financial_end_month=financial_end_month,
				charity_number=data.charity_number if data.charity_number else None,
				annual_income=float(data.annual_income),
				registration_type_id=data.registration_type_id,
				account_id=user.account_id,
				biography=data.biography,
				vision=data.vision,
				motto=data.motto,
				objectives=data.objectives if data.objectives else None,
				status=OrganizationStatus.DRAFT,
				created_by=user.id
			)

			self.db.add(organization)
			self.db.commit()
			self.db.refresh(organization)

			logger.info(f"Successfully created organization: {organization.name} (ID: {organization.id})")
			return serializer.to_organization_dto(organization)

		except IntegrityError as e:
			self.db.rollback()
			logger.error(f"Database integrity error while creating organization: {str(e)}")
			raise ApiException("Failed to create organization: Duplicate data detected")

		except Exception as e:
			self.db.rollback()
			logger.error(f"Unexpected error while creating organization: {str(e)}")
			raise ApiException("Failed to create organization")


	def get_organization_by_id(self, organization_id: str) -> Optional[OrganizationDto]:
		"""Retrieve a single organization by ID."""
		try:
			organization = self.db.query(Organization).filter(
				Organization.id == organization_id
			).first()

			if not organization:
				return None

			return serializer.to_organization_dto(organization)

		except Exception as e:
			logger.error(f"Failed to retrieve organization by ID {organization_id}: {str(e)}")
			raise ApiException("Failed to retrieve organization")


	def update_organization(self, organization_id: str, user: CurrentUser, data: OrganizationRequest) -> OrganizationDto:
		"""Update an existing organization."""
		try:
			organization = self.db.query(Organization).filter(
				Organization.id == organization_id
			).first()

			if not organization:
				raise ApiException("Organization not found")

			organization.name = data.name
			organization.abbreviation = data.abbreviation
			organization.organization_type_id = data.organization_type_id
			organization.district_id = data.district_id
			organization.financial_start_month = self._parse_date_string(data.financial_start_month)
			organization.financial_end_month = self._parse_date_string(data.financial_end_month)
			organization.charity_number = data.charity_number if data.charity_number else None
			organization.annual_income = float(data.annual_income)
			organization.registration_type_id = data.registration_type_id
			organization.biography = data.biography
			organization.vision = data.vision
			organization.motto = data.motto
			organization.objectives = data.objectives if data.objectives else None
			organization.updated_by = user.id
			organization.updated_at = datetime.utcnow()

			self.db.commit()
			self.db.refresh(organization)

			logger.info(f"Successfully updated organization: {organization.name} (ID: {organization.id})")
			return serializer.to_organization_dto(organization)

		except IntegrityError as e:
			self.db.rollback()
			logger.error(f"Database integrity error while updating organization: {str(e)}")
			raise ApiException("Failed to update organization: Duplicate data detected")

		except ApiException:
			self.db.rollback()
			raise

		except Exception as e:
			self.db.rollback()
			logger.error(f"Unexpected error while updating organization: {str(e)}")
			raise ApiException("Failed to update organization")


	def delete_organization(self, organization_id: str, user: CurrentUser) -> bool:
		"""Delete an organization (soft delete by changing status)."""
		try:
			organization = self.db.query(Organization).filter(
				Organization.id == organization_id
			).first()

			if not organization:
				raise ApiException("Organization not found")

			organization.status = OrganizationStatus.INACTIVE
			organization.voided = True
			organization.voided_by = user.id
			organization.void_reason = "So"

			self.db.commit()

			logger.info(f"Successfully deleted organization: {organization.name} (ID: {organization.id})")
			return True

		except ApiException:
			self.db.rollback()
			raise

		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to delete organization {organization_id}: {str(e)}")
			raise ApiException("Failed to delete organization")
