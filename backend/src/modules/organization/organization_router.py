from typing import List

from fastapi import APIRouter, status

from src.core.dtos.organization_dtos import OrganizationDto
from src.core.shared_schema import BaseResponse
from src.modules.organization import organization_controller as controller

router = APIRouter(tags=["organizations"])

router.add_api_route(
	path="/",
	endpoint=controller.create_organization_handler,
    methods=["POST"],
    response_model=BaseResponse[OrganizationDto],
    status_code=status.HTTP_201_CREATED
)
router.add_api_route(
	path="/",
	endpoint=controller.fetch_organizations_handler,
    methods=["GET"],
   	response_model=BaseResponse[List[OrganizationDto]],
)


