from typing import List
from pydantic import UUID4
from src.config.db.models import Complaint
from src.core.logger import logger
from src.core.base.application_repository import ApplicationRepository
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser, FilterResponse, VoidRequest
from src.core.utils import serializer

from .complaint_schema import (ComplaintDTO, ComplaintFilter,ComplaintRequest)


class ComplaintService(ApplicationRepository):
	def __init__(self):
		super().__init__()

	def retrieve_complaints(self, filter: ComplaintFilter) -> FilterResponse[List[ComplaintDTO]]:
		
		try:
			query = self.db.query(Complaint).filter(Complaint.voided == False)

			if filter.priority:
				query = query.filter(Complaint.priority == filter.priority)

			if filter.organization_id:
				query = query.filter(Complaint.organization_id == filter.organization_id)

			if filter.category_id:
				query = query.filter(Complaint.category_id == filter.category_id)

			total = query.count()
			
			if filter.paginate:
				offset = (filter.page - 1) * filter.size
				query = query.offset(offset).limit(filter.size)


			rows = query.all()
			complaints = [serializer.to_complaint_dto(row) for row in rows]

			return  FilterResponse[List[ComplaintDTO]](data = complaints, total_count = total)
		
		except Exception as e:
			logger.error(f"Failed to retrieve complaints: {str(e)}")
			raise ApiException("Failed to retrieve complaints")
		

	def create_complaint(self, payload: ComplaintRequest) -> ComplaintDTO:
		try:
			complaint = Complaint(
				id = payload.id,				 
				title = payload.title,			
				summary = payload.summary,
				priority= payload.priority,
				organization_id = payload.organization_id, 
				is_anonymous = payload.is_anonymous,
				category_id = payload.category_id,
				complainant_id =  payload.complainant_id
			)
			self.db.add(complaint)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)		
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to add complaint: {str(e)}")
			raise ApiException("Failed to add complaint")
		

	def update_complaint(self, id: UUID4, payload: ComplaintRequest) -> ComplaintDTO:
		try:
			complaint = self.db.query(Complaint).filter(Complaint.id == id).first()

			if complaint is None:
				raise ApiException("Complaint ID not found")
						
			complaint.title = payload.title,			
			complaint.summary = payload.summary,
			complaint.priority= payload.priority,
			complaint.organization_id = payload.organization_id, 
			complaint.is_anonymous = payload.is_anonymous,
			complaint.category_id = payload.category_id,
			complaint.complainant_id =  payload.complainant_id
			
			self.db.add(complaint)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)		
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to add complaint: {str(e)}")
			raise ApiException("Failed to add complaint")
			
	def void_complaint(self, id: UUID4, user: CurrentUser, payload:VoidRequest) -> ComplaintDTO:
		try:
			complaint = self.db.query(Complaint).filter(Complaint.id == id).first()

			if complaint is None:
				raise ApiException("Complaint ID not found")
			
			complaint.voided = True,
			complaint.voided_by = user.id,
			complaint.void_reason = payload.void_reason		
			
			self.db.add(complaint)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)		
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to add complaint: {str(e)}")
			raise ApiException("Failed to add complaint")
		