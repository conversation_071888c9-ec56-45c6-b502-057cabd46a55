from datetime import datetime
from typing import Optional

from pydantic import BaseModel, <PERSON>
from pydantic.types import UUID4

from src.core.shared_schema import BaseRequest


class ComplaintRequest(BaseModel):
    id: Optional[UUID4]
    title: str
    summary: str
    priority: int
    organization_id: UUID4
    is_anonymous: bool =  Field(default=False)
    category_id: UUID4
    complainant_id:  Optional[UUID4]


class ComplaintDTO(BaseModel):
    id: UUID4
    title: str
    summary: str
    priority: int
    organization_id: UUID4
    is_anonymous: bool
    category_id: UUID4
    complainant_id: UUID4
    created_at: datetime
    updated_at: datetime


class ComplaintFilter(BaseRequest):
    priority: Optional[int] = Field(default=None, description="Priority")
    category_id: Optional[UUID4] = Field(default=None, description="Category ID")
    organization_id: Optional[UUID4] = Field(default=None, description="Organisation ID")



