from typing import List
from fastapi import APIRouter
from src.modules.complaint import complaint_controller  as controller
from src.modules.complaint import complaint_schema as schema
from src.core.shared_schema import BaseResponse



router = APIRouter()

router.add_api_route(
    path="/complaints",
    endpoint=controller.fetch_complaints_handler,
    response_model=BaseResponse[List[schema.ComplaintDTO]],
    methods=['GET'],
    description = "Get all the complaints"
)

router.add_api_route(
	path='/complaints',
	endpoint=controller.create_complaints_handler,
	response_model=BaseResponse[schema.ComplaintDTO],
	methods=['POST'],
	description="Create a complaint"
)

router.add_api_route(
	path='/complaints/{id}',
	endpoint=controller.update_complaints_handler,
	response_model=BaseResponse[schema.ComplaintDTO],
	methods=['PUT'],
	description="Update a complaint"
)

router.add_api_route(
	path='/complaints/{id}',
	endpoint=controller.void_complaints_handler,
	response_model=BaseResponse[bool],
	methods=['DELETE'],
	description="Delete Complaint"
)
