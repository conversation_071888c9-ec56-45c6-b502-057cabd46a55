import os
import subprocess
import sys
import click
from dotenv import load_dotenv
import psycopg2


def conn():
  load_dotenv()
  dbname = os.getenv("POSTGRES_DB")
  user = os.getenv("POSTGRES_USER")
  password = os.getenv("POSTGRES_PASSWORD")
  host = os.getenv("POSTGRES_HOST")
  port = os.getenv("POSTGRES_PORT")

  return psycopg2.connect(
      dbname=dbname,
      user=user,
      password=password,
      host=host,
      port=port
  )

def create_superuser():
    from src.config.db.database import get_db
    from src.config.db.models.account import Account, AccountStatus, AccountType
    from src.config.db.models.session import Session
    from src.config.db.models.user import User
    from src.core.services.encryption_service import EncryptionService

    encryption_service = EncryptionService()

    db: Session = get_db()
    username = os.getenv("SUPERUSER_USERNAME", "superuser")
    password = os.getenv("SUPERUSER_PASSWORD", "letmein")
    try:
      # Check if superuser already exists
        superuser = db.query(Account).filter(Account.handle == username).first()
        if superuser is None:
          try:    
            account = Account(
                type=AccountType.ORG,
                status=AccountStatus.INACTIVE,
                handle=username,
            )
            db.add(account)
            db.commit()
            superuser = db.query(Account).filter(Account.handle == username).first()
          except Exception as e:
            print(f"Error creating superuser account: {e}")
            return

        hashed_password = encryption_service.hash_password(password)
          
        user = db.query(User).filter(User.account_id == superuser.id).first()
        if user is None:
          try:
            suser = User(
                first_name=os.getenv("SUPERUSER_FIRST_NAME", "Super"),
                middle_name=os.getenv("SUPERUSER_MIDDLE_NAME", "User"),
                last_name=os.getenv("SUPERUSER_LAST_NAME", "Admin"),
                email=str(os.getenv("SUPERUSER_EMAIL", "<EMAIL>")),
                hashed_password=hashed_password,
                is_external=False
            )
            suser.account_id = superuser.id
            db.add(suser)
            db.commit()
          except Exception as e:
            print(f"Error creating superuser user: {e}")
            return
        db.flush()
    finally:
        db.close()


def create_database():
  connection = conn()
  connection.autocommit = True
  cur = connection.cursor()
  try:
    dbname = os.getenv("POSTGRES_DB")
    cur.execute(f"CREATE DATABASE {dbname};")
    print(f"✅ Database '{dbname}' created.")
  except psycopg2.errors.DuplicateDatabase:
      print(f"ℹ️ Database '{dbname}' already exists.")
  finally:
      cur.close()
      connection.close()

@click.group()
def cli():
  """FastAPI custom commands"""
  pass

@cli.group()
def db():
  """Database commands"""
  pass

@db.command()
def init():
    """Create database and apply migrations."""
    create_database()

    from alembic.config import Config
    from alembic import command
    alembic_cfg = Config("alembic.ini")
    command.upgrade(alembic_cfg, "head")
    print("✅ Migrations applied.")
    
    create_superuser()
    print("✅ Superuser created.")

@db.command(name="migrate")
def db_migrate():
  """Run database migrations using Alembic"""
  try:
    click.echo("Running database migrations...")
    result = subprocess.run(["alembic", "upgrade", "head"], check=True, capture_output=True, text=True)
    click.echo("✅ Database migrations completed successfully!")
    if result.stdout:
      click.echo(result.stdout)
  except subprocess.CalledProcessError as e:
    click.echo(f"❌ Migration failed: {e}", err=True)
    if e.stderr:
      click.echo(e.stderr, err=True)
    sys.exit(1)
  except FileNotFoundError:
    click.echo("❌ Alembic not found. Make sure it's installed and in your PATH.", err=True)
    sys.exit(1)

@db.command(name="seed")
def db_seed():
  """Seed the database with initial data"""
  try:
    click.echo("Seeding database...")

    from src.config.db.seed import run_seed
    run_seed()

    click.echo("✅ Database seeded successfully!")
  except ImportError as e:
    click.echo(f"❌ Could not import seed module: {e}", err=True)
    click.echo("Make sure src.config.db.seed exists and has a run_seed function")
    sys.exit(1)
  except Exception as e:
    click.echo(f"❌ Seeding failed: {e}", err=True)
    sys.exit(1)

if __name__ == "__main__":
  cli()
