from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from typing import Optional, Callable, TypeVar, Any

from src.modules.auth.auth_service import AuthService
from src.config.db.models import User
from src.core.shared_schema import BaseResponse, CurrentUser

T = TypeVar('T')

class AuthGuard:
	"""
	Authentication guard for FastAPI that provides access to the current user
	and protects routes requiring authentication.
	"""
	def __init__(self):
		self.auth_service = AuthService()
		self.oauth2_scheme = OAuth2PasswordBearer(tokenUrl="v1/auth/login")

	def current_user(self, token: str = Depends(OAuth2PasswordBearer(tokenUrl="v1/auth/login"))) -> CurrentUser:
		"""
		Dependency that retrieves the current authenticated user.
		Raises HTTPException with 401 status if authentication fails.

		Usage:
			@router.get("/me")
			async def read_users_me(user: User = Depends(auth_guard.current_user)):
				return user

		Returns:
			User: The authenticated user

		Raises:
			HTTPException: 401 Unauthorized if no valid user session exists
		"""
		try:
			user = self.auth_service.get_current_user(token)
			if user is None:
				raise HTTPException(
					status_code=status.HTTP_401_UNAUTHORIZED,
					detail=BaseResponse[None](
						success=False,
						errors=[{"message": "Authentication required. Please log in to access this resource."}]
					).model_dump(),
					headers={"WWW-Authenticate": "Bearer"},
				)
			return user
		except Exception as e:
			raise HTTPException(
				status_code=status.HTTP_401_UNAUTHORIZED,
				detail=BaseResponse[None](
					success=False,
					errors=[{"message": "Authentication required. Please log in to access this resource."}]
				).model_dump(),
				headers={"WWW-Authenticate": "Bearer"},
			)

	def current_user_id(self, token: str = Depends(OAuth2PasswordBearer(tokenUrl="v1/auth/login"))) -> int:
		"""
		Dependency that retrieves only the current user ID.
		Raises HTTPException with 401 status if authentication fails.

		Usage:
			@router.post("/products")
			async def create_product(
				product: ProductCreate,
				user_id: int = Depends(auth_guard.current_user_id)
			):
				return product_service.create_product(user_id, product)

		Returns:
			int: The authenticated user ID

		Raises:
			HTTPException: 401 Unauthorized if no valid user session exists
		"""
		user = self.current_user(token)
		return user.user_id

	def require_roles(self, allowed_roles: list[str]) -> Callable[[Any], Any]:
		"""
		Creates a dependency that ensures the user has one of the required roles.

		Args:
			allowed_roles: List of role names that are allowed to access the endpoint

		Usage:
			@router.delete("/users/{user_id}")
			async def delete_user(
				user_id: int,
				_: Any = Depends(auth_guard.require_roles(["admin"]))
			):
				return user_service.delete_user(user_id)

		Returns:
			Callable: A dependency function that checks if the user has the required role

		Raises:
			HTTPException: 401 Unauthorized if no valid user session exists
			HTTPException: 403 Forbidden if the user doesn't have the required role
		"""
		def role_checker(user: User = Depends(self.current_user)) -> User:
			user_roles = getattr(user, "roles", [])

			if not any(role in allowed_roles for role in user_roles):
				raise HTTPException(
					status_code=status.HTTP_403_FORBIDDEN,
					detail=BaseResponse[None](
						success=False,
						errors=[{"message": "Insufficient permissions to access this resource"}]
					).model_dump(),
				)
			return user

		return role_checker

	def optional_user(self, token: Optional[str] = Depends(OAuth2PasswordBearer(tokenUrl="v1/auth/login", auto_error=False))) -> Optional[User]:
		"""
		Dependency that retrieves the current user if authenticated, otherwise returns None.
		Useful for endpoints that work with or without authentication.

		Usage:
			@router.get("/products/{product_id}")
			async def get_product(
				product_id: int,
				user: Optional[User] = Depends(auth_guard.optional_user)
			):
				return product_service.get_product(product_id, user_id=user.user_id if user else None)

		Returns:
			Optional[User]: The authenticated user or None if not authenticated
		"""
		if token is None:
			return None

		try:
			return self.auth_service.get_current_user(token)
		except HTTPException:
			return None


auth_guard = AuthGuard()