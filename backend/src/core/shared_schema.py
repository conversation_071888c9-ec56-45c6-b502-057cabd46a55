from typing import Dict, Generic, List, Optional, TypeVar

from pydantic import UUID4, BaseModel, Field

DataT = TypeVar('DataT')


class BaseResponse(BaseModel, Generic[DataT]):
    data: Optional[DataT] = Field(default=None, description="The response payload")
    success: bool = Field(default=True, description="Indicates if the request was successful")
    errors: List[Dict[str, str]] = Field(default_factory=list, description="List of error messages")
    page: Optional[int] = Field(default=None, description="Current page")
    size: Optional[int] = Field(default=None, description="Number of items per page")
    total: Optional[int] = Field(default=None, description="Total items count")

    class Config:
        arbitrary_types_allowed = True


class BaseRequest(BaseModel):
    page: int = Field(default=1, ge=1, description="Current page")
    size: int = Field(default=15, ge=1, description="Number of items per page")
    paginate: bool = Field(default=True, description="Whether to create pagination or not")


class FilterResponse(BaseModel, Generic[DataT]):
    data: DataT
    total_count: int


class CurrentUser(BaseModel):
    id: UUID4
    username: str
    role_id: UUID4

class VoidRequest(BaseModel):
    void_reason: str
