from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel
from pydantic.types import UUID4

from src.config.db.models.base import Gender
from src.config.db.models.organization import OrganizationStatus
from src.modules.settings.settings_schema import CountryDto, LoadableItemDto


class BankDetailsDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	account_number: str
	branch_name: str
	bank_id: UUID4


class OrganizationAuditorDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	name: str
	email: str
	phone: str
	address: str
	is_active: bool


class FundingSourceDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	donor_id: UUID4
	currency_id: UUID4
	contact_person: str
	amount: float

class TargetGroup(BaseModel):
	id: UUID4
	organization_id: UUID4
	type_id: UUID4
	is_active: bool
	created_at: datetime
	updated_at: datetime


class LocationActivityDto(BaseModel):
	id: UUID4
	vdc_id: UUID4
	adc_id: UUID4
	organization_id: UUID4
	district_id: UUID4
	created_at: datetime
	updated_at: datetime

	class Config:
		"""Pydantic configuration."""
		from_attributes = True


class OrganizationSectorDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	sector_id: UUID4
	sector: Optional[LoadableItemDto]

	class Config:
		"""Pydantic configuration."""
		from_attributes = True


class DirectorDto(BaseModel):
	id: UUID4
	fullname: str
	email: str
	phone: str
	avatar: Optional[str]
	national_identifier: Optional[str]
	passport_number: Optional[str]
	gender: Gender
	position: str
	country_id: UUID4
	occupation: str
	timeframe: str
	qualification_id: UUID4
	organization_id: UUID4
	country: Optional[CountryDto]
	created_at: datetime

	class Config:
		"""Pydantic configuration."""
		from_attributes = True
		use_enum_values = True


class OrganizationDto(BaseModel):
	"""Schema for returning organization data."""

	id: UUID4
	name: str
	abbreviation: str
	organization_type_id: UUID4
	registration_number: str
	district_id: UUID4
	financial_start_month: str
	financial_end_month: str
	charity_number: Optional[str]
	annual_income: float
	registration_type_id: UUID4
	biography: Optional[str]
	vision: Optional[str]
	motto: Optional[str]
	objectives: Optional[str]
	status: OrganizationStatus
	account_id: UUID4
	created_at: Optional[str]
	updated_at: Optional[str]
	created_by: Optional[UUID4]
	updated_by: Optional[UUID4]

	class Config:
		"""Pydantic configuration."""
		from_attributes = True
		use_enum_values = True

