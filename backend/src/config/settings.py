from os import getenv

ENVIRONMENT = getenv("ENV", "development").lower()
SECRET_KEY = getenv("SECRET_KEY", "$3kur17y")
ACCESS_TOKEN_EXPIRE_MINUTES = float(getenv("REFRESH_TOKEN_EXPIRES_IN", "120000"))
REFRESH_TOKEN_EXPIRE_DAYS = float(getenv("ACCESS_TOKEN_EXPIRES_IN", "30"))
APP_NAME = getenv("APP_NAME", "myNGO")
SYSTEM_CODE = getenv("SYSTEM_CODE")
APP_VERSION = getenv("APP_VERSION", "1.0.0")
MAILJET_PUBLIC_KEY = getenv("MAILJET_PUBLIC_KEY")
MAILJET_PRIVATE_KEY = getenv("MAILJET_PRIVATE_KEY")

MINIO_ROOT_USER = getenv("MINIO_ROOT_USER", "ngora")
MINIO_ROOT_PASSWORD = getenv("MINIO_ROOT_PASSWORD", "ngora12345")
MINIO_ENDPOINT = getenv("MINIO_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = getenv("MINIO_ACCESS_KEY", "some-access-key")
MINIO_SECRET_KEY = getenv("MINIO_SECRET_KEY", "some-secret-key")
MINIO_BUCKET = getenv("MINIO_BUCKET", "myngo")
MINIO_SECURE = getenv("MINIO_SECURE", "false").lower() in ("true", "1", "yes")

def get_trusted_hosts() -> list[str]:
	hosts: str = getenv("ALLOWED_HOSTS", "*")
	return hosts.split(",")
