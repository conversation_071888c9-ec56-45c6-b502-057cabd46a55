"""added type to contact

Revision ID: 95f2c3ec0ac1
Revises: 0128f41cd1b9
Create Date: 2025-06-29 19:44:04.703838

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '95f2c3ec0ac1'
down_revision: Union[str, None] = '0128f41cd1b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contacts', sa.Column('type', sa.Enum('EMAIL', 'PHONE', 'ADDRESS', name='contacttype'), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contacts', 'type')
    # ### end Alembic commands ###
