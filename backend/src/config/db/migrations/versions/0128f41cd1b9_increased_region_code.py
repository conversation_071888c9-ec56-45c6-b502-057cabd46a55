"""increased region code

Revision ID: 0128f41cd1b9
Revises: f20300fe381d
Create Date: 2025-06-27 18:56:01.546094

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0128f41cd1b9'
down_revision: Union[str, None] = 'f20300fe381d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('regions', 'code',
               existing_type=sa.VARCHAR(length=5),
               type_=sa.String(length=10),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('regions', 'code',
               existing_type=sa.String(length=10),
               type_=sa.VARCHAR(length=5),
               existing_nullable=False)
    # ### end Alembic commands ###
