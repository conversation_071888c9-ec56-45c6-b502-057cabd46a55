"""increased currency code

Revision ID: f20300fe381d
Revises: 5ffc84628f67
Create Date: 2025-06-27 18:52:56.760132

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f20300fe381d'
down_revision: Union[str, None] = '5ffc84628f67'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'accounts', ['id'])
    op.create_unique_constraint(None, 'activities', ['id'])
    op.create_unique_constraint(None, 'activity_invitations', ['id'])
    op.create_unique_constraint(None, 'application_documents', ['id'])
    op.create_unique_constraint(None, 'application_fees', ['id'])
    op.create_unique_constraint(None, 'applications', ['id'])
    op.create_unique_constraint(None, 'area_development_committees', ['id'])
    op.create_unique_constraint(None, 'attendances', ['id'])
    op.create_unique_constraint(None, 'audit_logs', ['id'])
    op.create_unique_constraint(None, 'bank_details', ['id'])
    op.create_unique_constraint(None, 'complaint_attachments', ['id'])
    op.create_unique_constraint(None, 'complaint_statuses', ['id'])
    op.create_unique_constraint(None, 'complaints', ['id'])
    op.create_unique_constraint(None, 'contacts', ['id'])
    op.create_unique_constraint(None, 'countries', ['id'])
    op.alter_column('currencies', 'code',
               existing_type=sa.VARCHAR(length=2),
               type_=sa.String(length=5),
               existing_nullable=False)
    op.create_unique_constraint(None, 'currencies', ['id'])
    op.create_unique_constraint(None, 'departments', ['id'])
    op.create_unique_constraint(None, 'directors', ['id'])
    op.create_unique_constraint(None, 'districts', ['id'])
    op.create_unique_constraint(None, 'documents', ['id'])
    op.create_unique_constraint(None, 'donors', ['id'])
    op.create_unique_constraint(None, 'funding_sources', ['id'])
    op.create_unique_constraint(None, 'global_properties', ['id'])
    op.create_unique_constraint(None, 'invoice_documents', ['id'])
    op.create_unique_constraint(None, 'invoice_items', ['id'])
    op.create_unique_constraint(None, 'invoices', ['id'])
    op.create_unique_constraint(None, 'licence_fees', ['id'])
    op.create_unique_constraint(None, 'licences', ['id'])
    op.create_unique_constraint(None, 'loadable_items', ['id'])
    op.create_unique_constraint(None, 'location_activities', ['id'])
    op.create_unique_constraint(None, 'member_invitations', ['id'])
    op.create_unique_constraint(None, 'members', ['id'])
    op.create_unique_constraint(None, 'menus', ['id'])
    op.create_unique_constraint(None, 'notification_recipients', ['id'])
    op.create_unique_constraint(None, 'notifications', ['id'])
    op.create_unique_constraint(None, 'organization_auditors', ['id'])
    op.create_unique_constraint(None, 'organization_donors', ['id'])
    op.create_unique_constraint(None, 'organization_projects', ['id'])
    op.create_unique_constraint(None, 'organization_sectors', ['id'])
    op.create_unique_constraint(None, 'organization_staffs', ['id'])
    op.create_unique_constraint(None, 'organization_versions', ['id'])
    op.create_unique_constraint(None, 'organizations', ['id'])
    op.create_unique_constraint(None, 'payments', ['id'])
    op.create_unique_constraint(None, 'permits', ['id'])
    op.create_unique_constraint(None, 'print_histories', ['id'])
    op.create_unique_constraint(None, 'printed_licences', ['id'])
    op.create_unique_constraint(None, 'printed_permits', ['id'])
    op.create_unique_constraint(None, 'regions', ['id'])
    op.create_unique_constraint(None, 'role_permissions', ['id'])
    op.create_unique_constraint(None, 'roles', ['id'])
    op.create_unique_constraint(None, 'sessions', ['id'])
    op.create_unique_constraint(None, 'supporting_documents', ['id'])
    op.create_unique_constraint(None, 'target_groups', ['id'])
    op.create_unique_constraint(None, 'template_stage_roles', ['id'])
    op.create_unique_constraint(None, 'template_stage_triggers', ['id'])
    op.create_unique_constraint(None, 'template_stages', ['id'])
    op.create_unique_constraint(None, 'templates', ['id'])
    op.create_unique_constraint(None, 'traditional_authorities', ['id'])
    op.create_unique_constraint(None, 'user_departments', ['id'])
    op.create_unique_constraint(None, 'user_roles', ['id'])
    op.create_unique_constraint(None, 'users', ['id'])
    op.create_unique_constraint(None, 'village_development_committees', ['id'])
    op.create_unique_constraint(None, 'villages', ['id'])
    op.create_unique_constraint(None, 'workflow_stage_roles', ['id'])
    op.create_unique_constraint(None, 'workflow_stages', ['id'])
    op.create_unique_constraint(None, 'workflows', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'workflows', type_='unique')
    op.drop_constraint(None, 'workflow_stages', type_='unique')
    op.drop_constraint(None, 'workflow_stage_roles', type_='unique')
    op.drop_constraint(None, 'villages', type_='unique')
    op.drop_constraint(None, 'village_development_committees', type_='unique')
    op.drop_constraint(None, 'users', type_='unique')
    op.drop_constraint(None, 'user_roles', type_='unique')
    op.drop_constraint(None, 'user_departments', type_='unique')
    op.drop_constraint(None, 'traditional_authorities', type_='unique')
    op.drop_constraint(None, 'templates', type_='unique')
    op.drop_constraint(None, 'template_stages', type_='unique')
    op.drop_constraint(None, 'template_stage_triggers', type_='unique')
    op.drop_constraint(None, 'template_stage_roles', type_='unique')
    op.drop_constraint(None, 'target_groups', type_='unique')
    op.drop_constraint(None, 'supporting_documents', type_='unique')
    op.drop_constraint(None, 'sessions', type_='unique')
    op.drop_constraint(None, 'roles', type_='unique')
    op.drop_constraint(None, 'role_permissions', type_='unique')
    op.drop_constraint(None, 'regions', type_='unique')
    op.drop_constraint(None, 'printed_permits', type_='unique')
    op.drop_constraint(None, 'printed_licences', type_='unique')
    op.drop_constraint(None, 'print_histories', type_='unique')
    op.drop_constraint(None, 'permits', type_='unique')
    op.drop_constraint(None, 'payments', type_='unique')
    op.drop_constraint(None, 'organizations', type_='unique')
    op.drop_constraint(None, 'organization_versions', type_='unique')
    op.drop_constraint(None, 'organization_staffs', type_='unique')
    op.drop_constraint(None, 'organization_sectors', type_='unique')
    op.drop_constraint(None, 'organization_projects', type_='unique')
    op.drop_constraint(None, 'organization_donors', type_='unique')
    op.drop_constraint(None, 'organization_auditors', type_='unique')
    op.drop_constraint(None, 'notifications', type_='unique')
    op.drop_constraint(None, 'notification_recipients', type_='unique')
    op.drop_constraint(None, 'menus', type_='unique')
    op.drop_constraint(None, 'members', type_='unique')
    op.drop_constraint(None, 'member_invitations', type_='unique')
    op.drop_constraint(None, 'location_activities', type_='unique')
    op.drop_constraint(None, 'loadable_items', type_='unique')
    op.drop_constraint(None, 'licences', type_='unique')
    op.drop_constraint(None, 'licence_fees', type_='unique')
    op.drop_constraint(None, 'invoices', type_='unique')
    op.drop_constraint(None, 'invoice_items', type_='unique')
    op.drop_constraint(None, 'invoice_documents', type_='unique')
    op.drop_constraint(None, 'global_properties', type_='unique')
    op.drop_constraint(None, 'funding_sources', type_='unique')
    op.drop_constraint(None, 'donors', type_='unique')
    op.drop_constraint(None, 'documents', type_='unique')
    op.drop_constraint(None, 'districts', type_='unique')
    op.drop_constraint(None, 'directors', type_='unique')
    op.drop_constraint(None, 'departments', type_='unique')
    op.drop_constraint(None, 'currencies', type_='unique')
    op.alter_column('currencies', 'code',
               existing_type=sa.String(length=5),
               type_=sa.VARCHAR(length=2),
               existing_nullable=False)
    op.drop_constraint(None, 'countries', type_='unique')
    op.drop_constraint(None, 'contacts', type_='unique')
    op.drop_constraint(None, 'complaints', type_='unique')
    op.drop_constraint(None, 'complaint_statuses', type_='unique')
    op.drop_constraint(None, 'complaint_attachments', type_='unique')
    op.drop_constraint(None, 'bank_details', type_='unique')
    op.drop_constraint(None, 'audit_logs', type_='unique')
    op.drop_constraint(None, 'attendances', type_='unique')
    op.drop_constraint(None, 'area_development_committees', type_='unique')
    op.drop_constraint(None, 'applications', type_='unique')
    op.drop_constraint(None, 'application_fees', type_='unique')
    op.drop_constraint(None, 'application_documents', type_='unique')
    op.drop_constraint(None, 'activity_invitations', type_='unique')
    op.drop_constraint(None, 'activities', type_='unique')
    op.drop_constraint(None, 'accounts', type_='unique')
    # ### end Alembic commands ###
