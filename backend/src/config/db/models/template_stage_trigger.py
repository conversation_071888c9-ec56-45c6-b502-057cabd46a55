from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class ActionMode(str, Enum):
	"""Enumeration for action modes."""
	BEFORE = "BEFORE"
	AFTER = "AFTER"


class TemplateStageTrigger(BaseModel, AuditMixin):
	"""Model for template stage triggers."""

	__tablename__ = tables.template_stage_triggers

	id = primary_key()
	template_stage_id = foreign_key(f"{tables.template_stages}.id", nullable=False)
	function_id = foreign_key(f"{tables.loadable_items}.id", nullable=True)
	action_mode = Column(SQLEnum(ActionMode, nullable=False))

	# Relationships
	template_stage = relationship(
		"TemplateStage",
		back_populates="triggers",
		uselist=False,
	)
	function = relationship(
		"LoadableItem",
		back_populates="template_stage_triggers",
		uselist=False,
	)

	def __repr__(self):
		return f"<TemplateStageTrigger(id={self.id}, template_stage_id={self.template_stage_id}, function_id={self.function_id}, action_mode={self.action_mode})>"
