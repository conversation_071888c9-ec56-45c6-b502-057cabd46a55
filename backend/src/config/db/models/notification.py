from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class NotificationType(str, Enum):
	"""Enum for notification types."""
	ACTIVITY = "ACTIVITY"
	ALERT = "ALERT"
	GENERAL = "GENERAL"
	INFO = "INFO"
	WARN = "WARN"
	SUCCESS = "SUCCESS"

class NotificationPriority(str, Enum):
	"""Enum for notification priorities."""
	LOW = "LOW"
	MEDIUM = "MEDIUM"
	HIGH = "HIGH"
	URGENT = "URGENT"

class Notification(BaseModel, AuditMixin):
	"""Model for notifications."""

	__tablename__ = tables.notifications

	id = primary_key()
	title = Column(String(255), nullable=False)
	message = Column(String(1024), nullable=False)
	type = Column(SQLEnum(NotificationType), default=NotificationType.INFO, nullable=False)
	activity_id = foreign_key(f"{tables.activities}.id", nullable=True)
	sender_id = foreign_key(f"{tables.users}.id", nullable=True)
	priority = Column(SQLEnum(NotificationPriority), default=NotificationPriority.MEDIUM, nullable=False)

	sender = relationship(
		"User",
		back_populates="sent_notifications",
		foreign_keys=[sender_id],
		uselist=False,
	)
	activity = relationship(
		"Activity",
		back_populates="notifications",
		foreign_keys=[activity_id],
		uselist=False,
	)
	recipients = relationship(
		"NotificationRecipient",
		back_populates="notification",
		foreign_keys="NotificationRecipient.notification_id",
		cascade="all, delete-orphan"
	)

	def __repr__(self):
		return f"<Notification(id={self.id}, account_id={self.account_id}, title={self.title}, message={self.message}, is_read={self.is_read}, is_archived={self.is_archived})>"
