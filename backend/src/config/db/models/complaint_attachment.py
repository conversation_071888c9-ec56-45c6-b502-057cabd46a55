from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (BaseModel, NonForeignKeyAuditMixin,
                                       foreign_key, primary_key)


class ComplaintAttachment(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.complaint_attachments

	id = primary_key()
	complaint_id = foreign_key(f"{tables.complaints}.id")
	document_id = foreign_key(f"{tables.documents}.id")
	description = Column(String, nullable=False)

	# Relationships
	complaint = relationship(
		"Complaint",
		back_populates="attachments",
		uselist=False,
		foreign_keys=[complaint_id],
	)
	document = relationship(
		"Document",
		back_populates="complaint_attachments",
		uselist=False,
		foreign_keys=[document_id],
	)
	def __repr__(self):
		return f"<ComplaintAttachment(id={self.id}, complaint_id={self.complaint_id}, document_id={self.document_id}, description={self.description})>"
