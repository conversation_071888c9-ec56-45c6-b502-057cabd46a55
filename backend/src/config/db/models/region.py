from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, primary_key


class Region(BaseModel, AuditMixin):
	__tablename__ = tables.regions

	id = primary_key()
	name = Column(String(20), nullable=False, unique=True)
	code = Column(String(10), nullable=False, unique=True)
	description = Column(String(255), nullable=False)

	# Relationships
	districts = relationship(
		'District',
		back_populates='region',
		foreign_keys="District.region_id"
	)
