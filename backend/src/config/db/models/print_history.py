from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class PrintHistory(BaseModel, AuditMixin):
	"""Model for print history of documents."""

	__tablename__ = tables.print_histories

	id = primary_key()
	count = Column(Integer, nullable=True)

	# Relationships
	printed_licences = relationship(
		"PrintedLicence",
		back_populates="print_history",
		cascade="all, delete-orphan",
		foreign_keys="PrintedLicence.print_history_id"
	)
	printed_permits = relationship(
		"PrintedPermit",
		back_populates="print_history",
		cascade="all, delete-orphan",
		foreign_keys="PrintedPermit.print_history_id"
	)

	def __repr__(self):
		return f"<PrintHistory(id={self.id}, count={self.count})>"
