from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class ApplicationDocument(BaseModel, AuditMixin):
	__tablename__ = tables.application_documents

	id = primary_key()
	application_id = foreign_key(f"{tables.applications}.id")
	document_id = foreign_key(f"{tables.documents}.id")
	document_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)

	# Relationships
	application = relationship(
		"Application",
		back_populates="documents",
		uselist=False,
		foreign_keys=[application_id],
	)
	document = relationship(
		"Document",
		back_populates="application_documents",
		uselist=False,
		foreign_keys=[document_id],
	)
	document_type = relationship(
		"LoadableItem",
		back_populates="application_documents",
		uselist=False,
		foreign_keys=[document_type_id],
	)

	def __repr__(self):
		return f"<ApplicationDocument(id={self.id}, application_id={self.application_id}, document_id={self.document_id}, document_type_id={self.document_type_id})>"
