from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.settings import SYSTEM_CODE

from .base import AuditMixin, BaseModel, primary_key


class LoadableItem(BaseModel, AuditMixin):
	__tablename__ = tables.loadable_items

	id = primary_key()
	type = Column(String(50), unique=False, nullable=False)
	code = Column(String(50), unique=True, nullable=False)
	display_value = Column(String(255), unique=False, nullable=False)
	description = Column(String(255), unique=False, nullable=True)
	system_code = Column(String(15), unique=False, nullable=False, default=SYSTEM_CODE)

	# Relationships
	role_permissions = relationship(
    	"RolePermission",
     	back_populates="permission",
		foreign_keys="RolePermission.permission_id"
	)

	template_stage_triggers = relationship(
		"TemplateStageTrigger",
		back_populates="loadable_item",
		foreign_keys="TemplateStageTrigger.function_id",
		cascade="all, delete-orphan"
	)

	target_groups = relationship(
		"TargetGroup",
		back_populates="type",
		foreign_keys="TargetGroup.type_id",
		cascade="all, delete-orphan"
	)
	supporting_documents = relationship(
		"SupportingDocument",
		back_populates="document_type",
		foreign_keys="SupportingDocument.document_type_id"
	)
	permits = relationship("Permit", back_populates="permit_type", foreign_keys="Permit.permit_type_id")
	payments = relationship("Payment", back_populates="payment_mode", foreign_keys="Payment.payment_mode_id")
	organizations = relationship(
		"Organization",
		back_populates="type",
		foreign_keys="Organization.organization_type_id"
	)
	organization_staff_types = relationship(
     	"OrganizationStaff",
      	back_populates="staff",
      	foreign_keys="OrganizationStaff.staff_type_id"
    )
	organization_sectors = relationship(
     	"OrganizationSector",
      	back_populates="sector",
      	foreign_keys="OrganizationSector.sector_id"
    )
	organization_thematic_areas = relationship(
     	"OrganizationProject",
		back_populates="thematic_area",
		foreign_keys="OrganizationProject.thematic_area_id"
  	)
	activity_categories = relationship(
		"Activity",
		back_populates="category",
		foreign_keys="Activity.category_id",
		cascade="all, delete-orphan"
	)
	director_qualifications = relationship(
		"Director",
		back_populates="qualification",
		foreign_keys="Director.qualification_id",
		cascade="all, delete-orphan"
	)
	complaints = relationship(
		"Complaint",
		back_populates="category",
		foreign_keys="Complaint.category_id",
		cascade="all, delete-orphan"
	)
	application_documents = relationship(
		"ApplicationDocument",
		back_populates="document_type",
		uselist=False,
		foreign_keys="ApplicationDocument.document_type_id",
		cascade="all, delete-orphan"
	)
	bank_details = relationship(
		"BankDetail",
		back_populates="bank",
		uselist=False,
		foreign_keys="BankDetail.bank_id",
		cascade="all, delete-orphan"
	)
	template_stage_triggers = relationship(
		"TemplateStageTrigger",
		back_populates="function",
		foreign_keys="TemplateStageTrigger.function_id",
		cascade="all, delete-orphan"
	)

	def __repr__(self):
		return f"<LoadableItem(id={self.id}, type={self.type}, code={self.code}, value={self.display_value})>"
