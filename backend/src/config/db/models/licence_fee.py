from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Float, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, BasicStatus,
                                       primary_key)


class LicenceFee(BaseModel, AuditMixin):
	__tablename__ = tables.licence_fees

	id = primary_key()
	amount = Column(Float, nullable=False)
	code = Column(String, unique=True, nullable=False)
	name = Column(String, nullable=False)
	description = Column(String)
	status = Column(SQLEnum(BasicStatus), default=BasicStatus.ACTIVE, nullable=False)

	# Relationships
	application_fees = relationship(
		"ApplicationFee",
		back_populates="licence_fee",
		cascade="all, delete-orphan",
		foreign_keys="ApplicationFee.licence_fee_id",
	)

	def __repr__(self):
		return f"<LicenceFee(id={self.id}, code={self.code}, name={self.name}, amount={self.amount}, status={self.status})>"
