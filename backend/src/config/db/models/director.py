from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, Gender,
                                       foreign_key, primary_key)


class Director(BaseModel, AuditMixin):
	__tablename__ = tables.directors

	id = primary_key()
	fullname = Column(String(100), nullable=False)
	email = Column(String(100), nullable=False)
	phone = Column(String(100), nullable=False)
	avatar = Column(String(255))
	national_identifier = Column(String(255))
	passport_number = Column(String(255))
	gender = Column(SQLEnum(Gender), nullable=False)
	position = Column(String(100))
	country_id = foreign_key("countries.id", nullable=False)
	occupation = Column(String(100))
	timeframe = Column(String(100))
	qualification_id = foreign_key(f"{tables.loadable_items}.id")
	organization_id = foreign_key(f"{tables.organizations}.id")

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="directors",
		foreign_keys=[organization_id],
		uselist=False
	)
	country = relationship(
		"Country",
		back_populates="organization_directors",
		foreign_keys=[country_id],
		uselist=False
	)
	qualification = relationship(
		"LoadableItem",
		back_populates="director_qualifications",
		foreign_keys=[qualification_id],
		uselist=False
	)
