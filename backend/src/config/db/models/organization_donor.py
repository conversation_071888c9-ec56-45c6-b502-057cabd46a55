from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class OrganizationDonor(BaseModel, AuditMixin):
	"""Model for organization donors."""

	__tablename__ = tables.organization_donors

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	donor_id = foreign_key(f"{tables.donors}.id")
	is_active = Column(Boolean, default=True, nullable=False)
	amount = Column(Float, default=0.0, nullable=False)

	# relationships
	donor = relationship("Donor", back_populates="organizations", foreign_keys=[donor_id])
	organization = relationship("Organization", back_populates="organization_donors")

	def __repr__(self):
		return f"<OrganizationDonor(id={self.id}, organization_id={self.organization_id}, is_active={self.is_active})>"
