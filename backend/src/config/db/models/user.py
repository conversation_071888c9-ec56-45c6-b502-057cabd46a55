from sqlalchemy import <PERSON><PERSON><PERSON>, Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import (BaseModel, Gender, NonForeignKeyAuditMixin, foreign_key,
                   primary_key)


class User(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.users

	id = primary_key()
	first_name = Column(String(50), nullable=False)
	middle_name = Column(String(50), nullable=True)
	last_name = Column(String(50), nullable=False)
	email = Column(String(100), unique=True, nullable=False)
	hashed_password = Column(String(255), nullable=False)
	account_id = foreign_key("accounts.id", nullable=False)
	is_external = Column(Boolean, default=True)
	gender = Column(SQLEnum(Gender), nullable=True)

	# Relationships
	account = relationship("Account", foreign_keys=[account_id], back_populates="user")

	organization_memberships = relationship(
		"Member",
		back_populates="user",
		foreign_keys="[Member.user_id]",
		cascade="all, delete-orphan"
	)

	user_department = relationship(
		"UserDepartment",
		foreign_keys="UserDepartment.user_id",
		uselist = False,
		back_populates="user"
	)
	user_role = relationship(
		"UserRole",
		foreign_keys="UserRole.user_id",
		uselist = False,
		back_populates="user"
	)
	sessions = relationship(
		"Session",
		foreign_keys="Session.user_id",
		uselist=False,
		back_populates="user"
	)
	approved_workflow_stages = relationship(
		"WorkflowStage",
		foreign_keys="WorkflowStage.approved_by",
		back_populates="approver",
	)
	sent_notifications = relationship(
		"Notification",
		foreign_keys="Notification.sender_id",
		back_populates="sender",
		uselist=True,
		cascade="all, delete-orphan"
	)
	sent_member_invitations = relationship(
		"MemberInvitation",
		foreign_keys="MemberInvitation.inviter_user_id",
		back_populates="inviter",
		uselist=True,
		cascade="all, delete-orphan"
	)
	audit_logs = relationship(
		"AuditLog",
		back_populates="user",
		foreign_keys="AuditLog.user_id",
		uselist=True,
		cascade="all, delete-orphan"
	)

	@property
	def organizations(self):
		"""Get all active organizations for this user"""
		return [membership.organization for membership in self.organization_memberships if membership.is_active]

	# Organizations created by this user
	created_organizations = relationship(
		"Organization",
		primaryjoin="User.id == Organization.created_by",
		viewonly=True
	)

	def __repr__(self):
		return f"<User(id={self.id}, username={self.username}, account_id={self.account_id})>"
