from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import BaseModel, NonForeignKeyAuditMixin, primary_key


class AccountType(str, Enum):
	ORG = "ORG"
	USER = "USER"

class AccountStatus(str, Enum):
	ACTIVE = "ACTIVE"
	INACTIVE = "INACTIVE"
	SUSPENDED = "SUSPENDED"


class Account(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.accounts

	id = primary_key()
	handle = Column(String(15), unique=True, nullable=False)
	type = Column(SQLEnum(AccountType), nullable=False)
	status = Column(SQLEnum(AccountStatus), nullable=False, default=AccountStatus.INACTIVE)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="account",
		uselist=False,
		foreign_keys="Organization.account_id",
		cascade="all, delete-orphan"
	)
	user = relationship(
		"User",
		back_populates="account",
		uselist=False,
		foreign_keys="User.account_id",
		cascade="all, delete-orphan"
	)
	supporting_documents = relationship(
		"SupportingDocument",
		back_populates="account",
		cascade="all, delete-orphan",
		foreign_keys="SupportingDocument.account_id"
	)
	contacts = relationship(
		"Contact",
		back_populates="account",
		uselist=True,
		foreign_keys="Contact.account_id",
		cascade="all, delete-orphan"
	)
	complaints = relationship(
		"Complaint",
		back_populates="complainant",
		uselist=True,
		foreign_keys="Complaint.complainant_id",
		cascade="all, delete-orphan"
	)
	attendances = relationship(
		"Attendance",
		back_populates="account",
		uselist=True,
		foreign_keys="Attendance.account_id",
		cascade="all, delete-orphan"
	)
	activity_invitations = relationship(
		"ActivityInvitation",
		back_populates="account",
		uselist=True,
		foreign_keys="ActivityInvitation.account_id",
		cascade="all, delete-orphan"
	)
	received_notifications = relationship(
		"NotificationRecipient",
		back_populates="recipient",
		uselist=True,
		foreign_keys="NotificationRecipient.account_id",
		cascade="all, delete-orphan"
	)

	def __repr__(self):
		return f"<Account(id={self.id}, username={self.username}, email={self.email})>"
