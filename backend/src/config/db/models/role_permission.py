from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import AuditMixin, BaseModel, foreign_key, primary_key


class RolePermission(BaseModel, AuditMixin):
	"""RolePermission model representing the association between roles and permissions."""
	__tablename__ = tables.role_permissions

	id = primary_key()
	role_id = foreign_key(f'{tables.roles}.id', nullable=False)
	permission_id = foreign_key(f'{tables.loadable_items}.id', nullable=False)

	role = relationship("Role", back_populates="role_permissions", foreign_keys=[role_id])
	permission = relationship("LoadableItem", back_populates="role_permissions", foreign_keys=[permission_id])

	def __repr__(self):
		return f"<RolePermission(role_id={self.role_id}, permission_id={self.permission_id})>"
