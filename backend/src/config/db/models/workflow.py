from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class Workflow(BaseModel, AuditMixin):
	"""Model for workflows."""

	__tablename__ = tables.workflows

	id = primary_key()
	template_id = foreign_key(f"{tables.templates}.id", nullable=False)
	application_id = foreign_key(f"{tables.applications}.id", nullable=False)

	# Relationships
	template = relationship(
		"Template",
		back_populates="workflows",
		uselist=False,
	)
	application = relationship(
		"Application",
		back_populates="workflows",
		uselist=False,
	)
	stages = relationship(
		"WorkflowStage",
		back_populates="workflow",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="WorkflowStage.workflow_id",
	)
	def __repr__(self):
		return f"<Workflow(id={self.id}, template_id={self.template_id}, application_id={self.application_id})>"
