from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float, Integer, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class TemplateStage(BaseModel, AuditMixin):
	"""Model for template stages."""

	__tablename__ = tables.template_stages

	id = primary_key()
	template_id = foreign_key(f"{tables.templates}.id", nullable=False)
	name = Column(String, nullable=False)
	description = Column(String, nullable=True)
	is_active = Column(Boolean, default=True)
	position = Column(Integer, nullable=False)

	# Relationships
	template = relationship(
		"Template",
		back_populates="stages",
		uselist=False,
	)
	triggers = relationship(
		"TemplateStageTrigger",
		back_populates="template_stage",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="TemplateStageTrigger.template_stage_id",
	)
	roles = relationship(
		"TemplateStageRole",
		back_populates="template_stage",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="TemplateStageRole.template_stage_id",
	)
	workflow_stages = relationship(
		"WorkflowStage",
		back_populates="template_stage",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="WorkflowStage.template_stage_id",
	)

	def __repr__(self):
		return f"<TemplateStage(id={self.id}, template_id={self.template_id}, name={self.name})>"
