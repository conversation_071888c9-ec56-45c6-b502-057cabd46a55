from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import BaseModel, NonForeignKeyAuditMixin, primary_key


class Role(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.roles

	id = primary_key()
	code = Column(String, nullable=False, unique=True)
	name = Column(String, nullable=False)
	description = Column(String, nullable=True)

	# Relationships
	role_permissions = relationship(
		"RolePermission",
		back_populates="role",
		cascade="all, delete-orphan"
	)
	user_roles = relationship(
		"UserRole",
		foreign_keys="UserRole.role_id",
		uselist = False,
		back_populates="role"
	)

	workflow_stage_roles = relationship(
		"WorkflowStageRole",
		back_populates="role",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="WorkflowStageRole.role_id",
	)

	template_stage_roles = relationship(
		"TemplateStageRole",
		back_populates="role",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="TemplateStageRole.role_id",
	)

	def __repr__(self):
		return f"<Role(id={self.id}, code={self.code}, name={self.name}, description={self.description})>"
