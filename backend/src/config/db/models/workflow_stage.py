from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class WorkflowStageStatus(str, Enum):
	"""Enum for workflow stage status."""
	PENDING = "PENDING"
	IN_REVIEW = "IN_REVIEW"
	COMPLETED = "COMPLETED"
	APPROVED = "APPROVED"
	REJECTED = "REJECTED"


class WorkflowStage(BaseModel, AuditMixin):
	"""Model for workflow stages."""

	__tablename__ = tables.workflow_stages

	id = primary_key()
	workflow_id = foreign_key(f"{tables.workflows}.id", nullable=False)
	template_stage_id = foreign_key(f"{tables.template_stages}.id", nullable=True)
	approved_by = foreign_key(f"{tables.users}.id", nullable=True)
	status = Column(SQLEnum(WorkflowStageStatus, nullable=False), default=WorkflowStageStatus.PENDING)

	# Relationships
	workflow = relationship(
		"Workflow",
		back_populates="stages",
		uselist=False,
	)
	approver = relationship(
		"User",
		back_populates="approved_workflow_stages",
		foreign_keys=[approved_by],
		uselist=False,
	)
	template_stage = relationship(
		"TemplateStage",
		back_populates="workflow_stages",
		uselist=False,
	)
	roles = relationship(
		"WorkflowStageRole",
		back_populates="workflow_stage",
		lazy="joined",
		cascade="all, delete-orphan",
		foreign_keys="WorkflowStageRole.workflow_stage_id",
	)

	def __repr__(self):
		return f"<WorkflowStage(id={self.id}, workflow_id={self.workflow_id})>"
