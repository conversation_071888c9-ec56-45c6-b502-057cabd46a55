from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class PrintedLicence(AuditMixin, BaseModel):
	"""Model for printed licences issued to organizations."""

	__tablename__ = tables.printed_licences

	id = primary_key()
	print_history_id = foreign_key(f"{tables.print_histories}.id", nullable=False)
	licence_id = foreign_key(f"{tables.licences}.id", nullable=False)

	# Relationships
	print_history = relationship(
		"PrintHistory",
		back_populates="printed_licences",
		uselist=False,
	)
	licence = relationship(
		"Licence",
		back_populates="printed_licences",
		uselist=False,
	)

	def __repr__(self):
		return f"<PrintedLicence(id={self.id}, print_history_id={self.print_history_id}, licence_id={self.licence_id})>"
