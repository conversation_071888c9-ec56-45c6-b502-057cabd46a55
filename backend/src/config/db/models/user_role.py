from sqlalchemy.orm import relationship

from src.config.db.tables import tables

from .base import AuditMixin, BaseModel, foreign_key, primary_key


class UserRole(BaseModel, AuditMixin):
	__tablename__ = tables.user_roles

	id = primary_key()
	role_id = foreign_key("roles.id")
	user_id = foreign_key("users.id")

	role = relationship(
		"Role",
		foreign_keys=[role_id],
		back_populates="user_roles"
	)
	user = relationship(
		"User",
		foreign_keys=[user_id],
		back_populates="user_role",
		uselist=False
	)

