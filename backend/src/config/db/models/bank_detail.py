from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class BankDetail(BaseModel, AuditMixin):
	"""Table for storing bank details of accounts."""
	__tablename__ = tables.bank_details

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	account_number = Column(String(50), nullable=False, unique=True)
	branch_name = Column(String(100), nullable=True)
	bank_id = foreign_key(f"{tables.loadable_items}.id", nullable=True)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="bank_details",
		uselist=False,
		foreign_keys=[organization_id],
	)
	bank = relationship(
		"LoadableItem",
		back_populates="bank_details",
		uselist=False,
		foreign_keys=[bank_id],
	)

	def __repr__(self):
		return f"<BankDetail(id={self.id}, organization_id={self.organization_id}, branch_name={self.branch_name})>"
