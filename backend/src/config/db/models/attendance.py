from sqlalchemy import Column, String
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key, timestamp_column)


class Attendance(BaseModel, AuditMixin):
	__tablename__ = tables.attendances

	id = primary_key()
	invitation_id = foreign_key(f"{tables.activity_invitations}.id")
	attendee_name = Column(String, nullable=False)
	attendee_contact = Column(JSONB)
	representing_org = Column(String)
	account_id = foreign_key(f"{tables.accounts}.id", nullable=True)
	checkin_at = timestamp_column(use_default=False, nullable=True)
	checkout_at = timestamp_column(use_default=False, nullable=True)

	# Relationships
	invitation = relationship(
		"ActivityInvitation",
		back_populates="attendances",
		uselist=False,
		foreign_keys=[invitation_id],
	)
	account = relationship(
		"Account",
		back_populates="attendances",
		uselist=False,
		foreign_keys=[account_id],
	)

	def __repr__(self):
		return f"<Attendance(id={self.id}, invitation_id={self.invitation_id}, attendee_name={self.attendee_name}, attendee_contact={self.attendee_contact}, representing_org={self.representing_org}, account_id={self.account_id}, checkin_at={self.checkin_at}, checkout_at={self.checkout_at})>"
