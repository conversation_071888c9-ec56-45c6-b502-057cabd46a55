from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class SupportingDocument(BaseModel, AuditMixin):
	"""Model for supporting documents."""

	__tablename__ = tables.supporting_documents

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	document_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	account_id = foreign_key(f"{tables.accounts}.id", nullable=True)
	document_id = foreign_key(f"{tables.documents}.id", nullable=True)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="supporting_documents",
		uselist=False,
	)
	document_type = relationship(
		"LoadableItem",
		back_populates="supporting_documents",
		uselist=False,
	)
	document = relationship(
		"Document",
		back_populates="supporting_documents",
		uselist=False,
	)

	account = relationship("Account", back_populates="supporting_documents", uselist=False)

	def __repr__(self):
		return f"<SupportingDocument(id={self.id}, organization_id={self.organization_id}, document_id={self.document_id})>"
