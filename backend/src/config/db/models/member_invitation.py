from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, InvitationStatus,
                                       foreign_key, primary_key,
                                       timestamp_column)
from src.config.db.models.member import MemberRole


class MemberInvitation(BaseModel, AuditMixin):
	__tablename__ = tables.member_invitations

	id = primary_key()
	inviter_user_id = foreign_key(f"{tables.users}.id")
	organization_id = foreign_key(f"{tables.organizations}.id")
	inviter_user_id = foreign_key(f"{tables.users}.id", nullable=True)
	invited_email = Column(String(255), nullable=True)
	code = Column(String(6), nullable=True)
	status = Column(SQLEnum(InvitationStatus), default=InvitationStatus.PENDING, nullable=False)
	role = Column(SQLEnum(MemberRole), nullable=False, default=MemberRole.MEMBER)
	expires_at = timestamp_column(use_default=False, nullable=False)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="member_invitations",
		foreign_keys=[organization_id],
		uselist=False,
	)
	inviter = relationship(
		"User",
		back_populates="sent_member_invitations",
		foreign_keys=[inviter_user_id],
		uselist=False,
	)

	def __repr__(self):
		return f"<MemberInvitation(id={self.id}, organization_id={self.organization_id}, invited_email={self.invited_email}, status={self.status})>"
