from sqlalchemy import Column, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class InvoiceItem(BaseModel, AuditMixin):
	__tablename__ = tables.invoice_items

	id = primary_key()
	invoice_id = foreign_key(f"{tables.invoices}.id")
	application_id = foreign_key(f"{tables.applications}.id")
	amount = Column(Float, default=0.0)

	# Relationships
	licence = relationship(
		"Licence",
		back_populates="invoice_item",
		foreign_keys="Licence.invoice_item_id",
		uselist=False
	)
	invoice = relationship(
		"Invoice",
		back_populates="items",
		uselist=False,
		foreign_keys=[invoice_id]
	)
	application = relationship(
		"Application",
		back_populates="invoice_items",
		uselist=False,
		foreign_keys=[application_id]
	)
	permit = relationship(
		"Permit",
		back_populates="invoice_item",
		uselist=False,
		foreign_keys="Permit.invoice_item_id"
	)

	def __repr__(self):
		return f"<InvoiceItem(id={self.id}, invoice_id={self.invoice_id}, application_id={self.application_id}, amount={self.amount})>"
