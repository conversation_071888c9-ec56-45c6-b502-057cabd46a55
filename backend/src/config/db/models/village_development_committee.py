from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class VillageDevelopmentCommittee(BaseModel, AuditMixin):
	"""Model for village development committees."""

	__tablename__ = tables.village_development_committees

	id = primary_key()
	name = Column(Float, nullable=False)
	district_id = foreign_key(f"{tables.districts}.id", nullable=False)
	ta_id = foreign_key(f"{tables.traditional_authorities}.id", nullable=False)
	is_active = Column(Boolean, default=True)

	# Relationships
	district = relationship(
		"District",
		back_populates="village_development_committees",
		uselist=False,
	)
	ta = relationship(
		"TraditionalAuthority",
		back_populates="village_development_committees",
		uselist=False,
	)
	organization_activities = relationship(
		"LocationActivity",
		back_populates="vdc",
		foreign_keys="LocationActivity.vdc_id",
		cascade="all, delete-orphan",
	)

	def __repr__(self):
		return f"<VillageDevelopmentCommittee(id={self.id}, name={self.name})>"
