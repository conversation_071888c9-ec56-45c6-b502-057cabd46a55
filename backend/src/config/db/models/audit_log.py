from sqlalchemy import Column, String, Text
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import BaseModel, foreign_key, primary_key, uuid_column


class AuditLog(BaseModel):
	"""Table for tracking changes to entities"""
	__tablename__ = tables.audit_logs

	id = primary_key()
	table_name = Column(String(100), nullable=False)
	record_id = uuid_column(nullable=False)
	action = Column(String(20), nullable=False)
	old_values = Column(Text, nullable=True)
	new_values = Column(Text, nullable=True)
	user_id = foreign_key(f"{tables.users}.id", nullable=True)

	# Relationships
	user = relationship("User", back_populates="audit_logs", foreign_keys=[user_id])

	def __repr__(self):
		return f"<AuditLog(audit_log_id={self.audit_log_id}, table_name={self.table_name}, record_id={self.record_id}, action={self.action})>"
