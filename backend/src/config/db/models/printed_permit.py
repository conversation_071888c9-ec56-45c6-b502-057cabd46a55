from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class PrintedPermit(AuditMixin, BaseModel):
	"""Model for printed licences issued to organizations."""

	__tablename__ = tables.printed_permits

	id = primary_key()
	print_history_id = foreign_key(f"{tables.print_histories}.id", nullable=False)
	permit_id = foreign_key(f"{tables.permits}.id", nullable=False)

	# Relationships
	print_history = relationship(
		"PrintHistory",
		back_populates="printed_permits",
		uselist=False,
	)
	permit = relationship(
		"Permit",
		back_populates="printed_permits",
		uselist=False,
	)

	def __repr__(self):
		return f"<PrintedLicence(id={self.id}, print_history_id={self.print_history_id}, permit_id={self.permit_id})>"
