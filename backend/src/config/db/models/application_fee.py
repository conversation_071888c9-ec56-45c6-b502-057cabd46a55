from sqlalchemy import Column, Numeric
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class ApplicationFee(BaseModel, AuditMixin):
	__tablename__ = tables.application_fees

	id = primary_key()
	amount = Column(Numeric(precision=10, scale=2), nullable=False)
	licence_fee_id = foreign_key(f"{tables.licence_fees}.id")

	# Relationships
	applications = relationship(
		"Application",
		back_populates="application_fee",
		uselist=True,
		foreign_keys="Application.application_fee_id",
		cascade="all, delete-orphan"
	)
	licence_fee = relationship(
		"LicenceFee",
		back_populates="application_fees",
		uselist=False,
		foreign_keys=[licence_fee_id],
	)

	def __repr__(self):
		return f"<ApplicationFee(id={self.id}, amount={self.amount}, licence_fee_id={self.licence_fee_id})>"
