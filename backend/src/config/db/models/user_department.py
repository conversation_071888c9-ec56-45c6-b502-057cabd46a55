from sqlalchemy.orm import relationship

from src.config.db.tables import tables

from .base import AuditMixin, BaseModel, foreign_key, primary_key


class UserDepartment(BaseModel, AuditMixin):
	__tablename__ = tables.user_departments

	id = primary_key()
	department_id = foreign_key("departments.id")
	user_id = foreign_key("users.id")

	department = relationship(
		"Department",
		foreign_keys=[department_id],
		back_populates="user_departments",
  		uselist=False
	)
	user = relationship(
		"User",
		foreign_keys=[user_id],
		back_populates="user_department",
		uselist=False
	)


