from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class ContactType(str, Enum):
	EMAIL = "EMAIL"
	PHONE = "PHONE"
	ADDRESS = "ADDRESS"

class Contact(BaseModel, AuditMixin):
	__tablename__ = tables.contacts

	id = primary_key()
	account_id = foreign_key(f"{tables.accounts}.id")
	type = Column(SQLEnum(ContactType), nullable=False)
	details = Column(JSONB, nullable=False)

	# Relationship
	account = relationship(
		"Account",
		back_populates="contacts",
		foreign_keys=[account_id],
		uselist=False
	)

	def __repr__(self):
		return f"<Contact(id={self.id}, account_id={self.account_id}, details={self.details})>"
