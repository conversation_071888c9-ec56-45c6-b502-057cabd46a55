from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (BaseModel, NonForeignKeyAuditMixin,
                                       foreign_key, primary_key)


class ComplaintStatusType(str, Enum):
	OPEN = "OPEN"
	IN_PROGRESS = "IN_PROGRESS"
	CLOSED = "CLOSED"
	ESCALATED = "ESCALATED"
	RESOLVED = "RESOLVED"


class ComplaintStatus(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.complaint_statuses

	id = primary_key()
	type = Column(SQLEnum(ComplaintStatusType), default=ComplaintStatusType.OPEN, nullable=False)
	complaint_id = foreign_key(f"{tables.complaints}.id")
	resolution = Column(String)

	# Relationships
	complaint = relationship(
		"Complaint",
		back_populates="statuses",
		uselist=False,
		foreign_keys=[complaint_id],
	)

	def __repr__(self):
		return f"<ComplaintStatus(id={self.id}, status={self.status}, complaint_id={self.complaint_id}, resolution={self.resolution})>"
