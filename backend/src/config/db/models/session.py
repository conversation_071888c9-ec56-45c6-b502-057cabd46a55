from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>MP, Column, String
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from src.config.db.tables import tables

from .base import BaseModel, foreign_key, primary_key


class Session(BaseModel):
	__tablename__ = tables.sessions

	id = primary_key()
	user_id = foreign_key("users.id")
	device = Column(String(50))
	ip_address = Column(String(20))
	logged_out_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)

	# Relationships
	user = relationship(
		"User",
		foreign_keys=[user_id],
		uselist=False,
		back_populates="sessions"
	)
