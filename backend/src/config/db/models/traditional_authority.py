from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class TraditionalAuthority(BaseModel, AuditMixin):
	"""Model for traditional authorities."""

	__tablename__ = tables.traditional_authorities

	id = primary_key()
	name = Column(String, nullable=False)
	district_id = foreign_key(f"{tables.districts}.id", nullable=False)
	description = Column(String, nullable=True)

	# Relationships
	district = relationship(
		"District",
		back_populates="traditional_authorities",
		uselist=False,
	)
	village_development_committees = relationship(
		"VillageDevelopmentCommittee",
		back_populates="ta",
		cascade="all, delete-orphan",
		foreign_keys="VillageDevelopmentCommittee.ta_id",
	)
	villages = relationship(
		"Village",
		back_populates="ta",
		cascade="all, delete-orphan",
		foreign_keys="Village.ta_id",
	)

	def __repr__(self):
		return f"<TraditionalAuthority(id={self.id}, name={self.name})>"
