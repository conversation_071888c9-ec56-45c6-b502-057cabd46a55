from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class OrganizationStaff(BaseModel, AuditMixin):
	"""Model for organization staff."""

	__tablename__ = tables.organization_staffs

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	staff_type_id = foreign_key(f"{tables.loadable_items}.id")
	is_active = Column(Boolean, default=True, nullable=False)
	total = Column(Integer, default=0, nullable=False)

	# relationships
	staff = relationship("LoadableItem", back_populates="organization_staff_types")
	organization = relationship("Organization", back_populates="staffs")

	def __repr__(self):
		return f"<OrganizationStaff(id={self.id}, organization_id={self.organization_id}, is_active={self.is_active})>"
