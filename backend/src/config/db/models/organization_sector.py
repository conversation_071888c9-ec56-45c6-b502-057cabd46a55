from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Float
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)


class OrganizationSector(BaseModel, AuditMixin):
	"""Model for sectors."""

	__tablename__ = tables.organization_sectors

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id", nullable=False)
	sector_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="sectors",
		uselist=False,
	)
	sector = relationship(
		"LoadableItem",
		back_populates="organization_sectors",
		uselist=False,
	)

	def __repr__(self):
		return f"<Sector(id={self.id}, organization_id={self.organization_id}, sector_id={self.sector_id})>"
