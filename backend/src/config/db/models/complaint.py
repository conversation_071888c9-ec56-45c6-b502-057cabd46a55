from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, Text
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (BaseModel, NonForeignKeyAuditMixin,
                                       foreign_key, primary_key)


class Complaint(BaseModel, NonForeignKeyAuditMixin):
	__tablename__ = tables.complaints

	id = primary_key()
	title = Column(String, nullable=False)
	summary = Column(Text, nullable=False)
	priority = Column(Integer, nullable=False)
	organization_id = foreign_key(f"{tables.organizations}.id")
	is_anonymous = Column(Boolean, default=False)
	category_id = foreign_key(f"{tables.loadable_items}.id")
	complainant_id = foreign_key(f"{tables.accounts}.id")

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="lodged_complaints",
		uselist=False,
		foreign_keys="Complaint.organization_id",
	)
	complainant = relationship(
		"Account",
		back_populates="complaints",
		uselist=False,
		foreign_keys="Complaint.complainant_id",
	)
	category = relationship(
		"LoadableItem",
		back_populates="complaints",
		uselist=False,
		foreign_keys=[category_id]
	)
	statuses = relationship(
		"ComplaintStatus",
		back_populates="complaint",
		foreign_keys="ComplaintStatus.complaint_id",
		cascade="all, delete-orphan"
	)
	attachments = relationship(
		"ComplaintAttachment",
		back_populates="complaint",
		foreign_keys="ComplaintAttachment.complaint_id",
		cascade="all, delete-orphan"
	)

	def __repr__(self):
		return f"<Complaint(id={self.id}, title={self.title}, summary={self.summary}, priority={self.priority}, organization_id={self.organization_id}, is_anonymous={self.is_anonymous})>"
