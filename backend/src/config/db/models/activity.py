from enum import Enum

from sqlalchemy import Column, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String, Text
from sqlalchemy.orm import relationship

from src.config.db.models.base import (AuditMixin, BaseModel, foreign_key,
                                       primary_key)
from src.config.db.tables import tables


class ActivityVisibility(str, Enum):
	ALL = "ALL"
	LIMITED = "LIMITED"


class Activity(BaseModel, AuditMixin):
	__tablename__ = tables.activities

	id = primary_key()
	title = Column(String(255), nullable=False)
	category_id = foreign_key(f"{tables.loadable_items}.id")
	visibility = Column(SQLEnum(ActivityVisibility), default=ActivityVisibility.ALL)
	venue = Column(String(100), nullable=False)
	district_id = foreign_key(f"{tables.districts}.id")
	longitude = Column(String(20))
	latitude = Column(String(20))
	map_pin = Column(Text)
	start_date = Column(DateTime, nullable=False)
	end_date = Column(DateTime, nullable=False)
	summary = Column(Text, nullable=False)

	# Relationships
	notifications = relationship(
		"Notification",
		back_populates="activity",
		foreign_keys="Notification.activity_id",
		uselist=True,
		cascade="all, delete-orphan"
	)
	category = relationship(
		"LoadableItem",
		back_populates="activity_categories",
		foreign_keys=[category_id],
		uselist=False,
	)
	district = relationship(
		"District",
		back_populates="activities",
		foreign_keys=[district_id],
		uselist=False,
	)
	invitations = relationship(
		"ActivityInvitation",
		back_populates="activity",
		uselist=True,
		foreign_keys="ActivityInvitation.activity_id",
		cascade="all, delete-orphan"
	)

	def __repr__(self):
		return f"<Activity(id={self.id}, title={self.title}, start_date={self.start_date}, end_date={self.end_date})>"
