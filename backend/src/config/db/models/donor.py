from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import (AuditMixin, BaseModel, BasicStatus,
                                       foreign_key, primary_key)


class Donor(BaseModel, AuditMixin):
	__tablename__ = tables.donors

	id = primary_key()
	name = Column(String, nullable=False)
	description = Column(String, nullable=False)
	country_id = foreign_key(f"{tables.countries}.id")
	status = Column(SQLEnum(BasicStatus), nullable=False, default=BasicStatus.ACTIVE)

	# relationships
	organizations = relationship(
		"OrganizationDonor",
		back_populates="donor",
		foreign_keys="OrganizationDonor.donor_id",
		uselist=True,
	)
	funding_sources = relationship(
		"FundingSource",
		back_populates="donor",
		foreign_keys="FundingSource.donor_id",
		uselist=True,
	)

	def __repr__(self):
		return f"<Donor(id={self.id}, name={self.name}, country_id={self.country_id}, status={self.status})>"
