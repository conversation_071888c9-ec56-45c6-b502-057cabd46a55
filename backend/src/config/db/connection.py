"""
Database connection and session management.
"""
from typing import Any, Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import os
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")

engine = create_engine(
	DATABASE_URL,
	echo=True,
	pool_size=10,
	max_overflow=20,
	pool_pre_ping=True,
	pool_recycle=3600
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, Any, None]:
	"""
	Get a database session.

	Yields:
		Session: SQLAlchemy database session

	Usage:
		# As dependency injection (FastAPI)
		def get_user(db: Session = Depends(get_db)):
			return db.query(User).all()

		# Manual usage
		with get_db() as db:
			users = db.query(User).all()
	"""
	db = SessionLocal()
	try:
		yield db
	finally:
		db.close()


def get_db_session() -> Session:
	"""
	Get a database session for manual management.
	Remember to close the session when done.

	Returns:
		Session: SQLAlchemy database session

	Usage:
		db = get_db_session()
		try:
			users = db.query(User).all()
		finally:
			db.close()
	"""
	return SessionLocal()
