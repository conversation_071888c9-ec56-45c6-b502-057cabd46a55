import time
from sqlalchemy import Text, create_engine, event
from sqlalchemy.orm import sessionmaker, Session
import os
from dotenv import load_dotenv
import sqlparse
from rich.console import Console
from rich.text import Text
from rich.panel import Panel

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")

engine = create_engine(DATABASE_URL, echo=True)

class SoftDeleteSession(Session):
    def delete(self, instance):
        if hasattr(instance, "voided"):
            setattr(instance, "voided", True)
            self.add(instance)  # mark as dirty so it gets updated
        else:
            super().delete(instance)


SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db() -> Session:
    """Get a database session"""
    db = SessionLocal()
    try:
        return db
    except:
        db.close()
        raise


def sql_logger():
    console = Console()

    @event.listens_for(engine, "before_cursor_execute")
    def before_cursor_execute(
        conn, cursor, statement, parameters, context, executemany
    ):
        try:
            conn.info.setdefault("query_start_time", []).append(time.time())
            context._sql_query_name = statement.strip().split()[0].upper()
            return statement, parameters
        except Exception as e:
            return

    @event.listens_for(engine, "after_cursor_execute")
    def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        if not hasattr(conn, "info") or not conn.info.get("query_start_time"):
            return

        try:
            start = conn.info["query_start_time"].pop()
            duration = (time.time() - start) * 1000  # ms
            query_type = getattr(context, "_sql_query_name", "UNKNOWN").upper()

            # Map query type to model action and color
            action_map = {
                "SELECT": ("Load", "cyan"),
                "INSERT": ("Create", "green"),
                "UPDATE": ("Update", "yellow"),
                "DELETE": ("Destroy", "red"),
            }

            action, color = action_map.get(query_type, ("Query", "white"))

            # Try to guess model name from SQL
            try:
                table_name = statement.split("FROM")[1].strip().split()[0]
                model_name = table_name.capitalize()
            except IndexError:
                model_name = "UnknownModel"

            label = f"{model_name} {action}"

            # Format bound parameters as [["id", 42], ["LIMIT", 1]]
            param_list = []
            if isinstance(parameters, dict):
                for k, v in parameters.items():
                    param_list.append([k, v])
            elif isinstance(parameters, list):
                for p in parameters:
                    param_list.append(str(p))
            else:
                param_list = str(parameters)

            # Pretty print SQL
            formatted_sql = sqlparse.format(statement, reindent=True, keyword_case="upper")

            # Build colorful log panel
            title = Text(f"{label} ({duration:.2f}ms)", style=f"bold {color}")
            content = Text(formatted_sql, style="magenta")
            if param_list:
                content += Text("\nParameters: ", style="blue") + Text(
                    str(param_list), style="yellow"
                )

            console.print(Panel(content, title=title, border_style=color))
        except Exception as e:
            return


# --- Call this to enable logging ---
sql_logger()
