import json
import os
from typing import Any, Dict, List

import pandas as pd
from dotenv import load_dotenv

from src.config.db.database import get_db
from src.config.db.models import (Account, AccountType, Country, Currency,
                                  Department, District, LoadableItem, Region,
                                  RolePermission, User, UserRole)
from src.config.db.models.base import Gender
from src.config.db.models.role import Role

load_dotenv()

def parse_description(description: str) -> str:
	"""Formats description by replacing dots with commas and tildes with dots."""
	return description.replace('.', ',').replace('~', '.')


def get_file_path(filename: str) -> str:
	"""Returns the absolute path to a seed data file."""
	return os.path.abspath(f"./src/config/db/seed_data/{filename}")


def validate_columns(data: pd.DataFrame, required_columns: List[str]) -> List[Dict[str, Any]]:
	"""Validates that the DataFrame contains all required columns and returns records as dict."""
	missing = [col for col in required_columns if col not in data.columns]
	if missing:
		raise ValueError(f"CSV missing required columns: {missing}")
	return data.to_dict(orient='records')


def get_root_user() -> User:
	db = get_db()
	try:
		user = db.query(User).join(Account, User.account).filter(Account.handle == "root").first()
		return user
	except Exception as e:
		return None


def create_root_account():
	"""Creates a root systems account if it doesn't exist"""
	db = get_db()
	try:
		existing_account = db.query(Account).filter(Account.handle == 'root').first()
		if existing_account:
			print("✅ Root account already exists")
			return

		from src.core.services.encryption_service import EncryptionService
		encryption_service = EncryptionService()

		account = Account(
			handle='root',
			type=AccountType.USER
		)
		db.add(account)
		db.commit()

		root = db.query(Account).filter(Account.handle=='root').first()

		user = User(
			account_id = root.id,
			first_name = 'Root',
			last_name = 'User',
			hashed_password = encryption_service.hash_password("Admin@123"),
			email = "<EMAIL>",
			is_external = False,
			gender=Gender.MALE
		)
		db.add(user)
		db.commit()

		print("✅ Root account created successfully")

	except Exception as e:
		db.rollback()
		print(f"❌ Error creating root account: {str(e)}")
	finally:
		db.close()


def seed_countries() -> None:
	"""Seeds country data from countries.json file."""
	db = get_db()
	try:
		with open(get_file_path('countries.json'), 'r') as file:
			countries_data = json.load(file)

		added_count = 0
		for item in countries_data:
			existing_country = db.query(Country).filter(Country.short_code == item['code']).first()
			if not existing_country:
				country = Country(
					name=item['name'],
					short_code=item['code'],
					dial_code=item['dial_code'],
					flag=item['flag']
				)
				db.add(country)
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new countries (skipped existing ones)")

	except Exception as e:
		db.rollback()
		print(f"❌ Error seeding countries: {str(e)}")
		raise
	finally:
		db.close()


def seed_departments(user_id: str) -> None:
	"""Seeds department data from departments.csv file."""
	db = get_db()
	try:
		data = pd.read_csv(get_file_path("departments.csv"))
		departments = validate_columns(data, ['name', 'code', 'description'])

		added_count = 0
		for dept in departments:
			existing_dept = db.query(Department).filter(Department.code == dept['code'].strip().upper()).first()
			if not existing_dept:
				db.add(Department(
					code=dept['code'].strip().upper(),
					name=dept['name'].strip(),
					description=parse_description(dept['description'].strip()),
					created_by=user_id
				))
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new departments (skipped existing ones)")

	except Exception as e:
		db.rollback()
		print(f"❌ Failed to seed departments: {str(e)}")
		raise
	finally:
		db.close()


def update_admin_role(user_id: str) -> None:
	"""Assigns the Admin role to the root user if not already assigned."""
	db = get_db()
	try:
		role = db.query(Role).filter(Role.code == 'ROOT').first()

		if not role:
			print("❌ ROOT role not found")
			return

		user = db.query(User).filter(User.id == user_id).first()

		if not user:
			print("❌ User not found")
			return

		existing_user_role = db.query(UserRole).filter(
			UserRole.role_id == role.id,
			UserRole.user_id == user_id
		).first()

		if existing_user_role:
			print("✅ User already has ROOT role")
			return

		user_role = UserRole(
			role_id=role.id,
			user_id=user_id,
			created_by=user_id
		)

		db.add(user_role)
		db.commit()
		print("✅ Updated admin role")

	except Exception as e:
		db.rollback()
		print(f"❌ Failed to update admin role: {str(e)}")
		raise
	finally:
		db.close()


def get_loadable_items() -> List[Dict[str, Any]]:
	"""Collects all loadable items from various CSV files."""
	columns = ['type', 'code', 'display_value', 'description']

	files = [
		"permissions.csv",
		"focus_areas.csv",
		"activity_categories.csv",
		"complaint_categories.csv",
		"donors.csv",
		"organization_types.csv",
		"payment_modes.csv",
		"pillars.csv",
		"registration_types.csv",
		"sectors.csv",
		"staff_types.csv",
		"target_groups.csv",
		"thematic_areas.csv",
		"zones.csv",
		"document_types.csv"
	]

	all_items = []
	for file in files:
		items = validate_columns(pd.read_csv(get_file_path(file)), columns)
		all_items.extend(items)

	return all_items


def seed_role_permissions(user_id: str) -> None:
	"""Assigns all permissions to the Admin role if not already assigned."""
	db = get_db()
	try:
		permissions = db.query(LoadableItem).filter(LoadableItem.type == 'PERMISSION').all()
		user_role = db.query(UserRole).filter(UserRole.user_id == user_id).first()

		if not user_role:
			print("❌ User role not found")
			return

		added_count = 0
		for permission in permissions:
			existing_permission = db.query(RolePermission).filter(
				RolePermission.permission_id == permission.id,
				RolePermission.role_id == user_role.role_id
			).first()

			if not existing_permission:
				role_permission = RolePermission(
					permission_id=permission.id,
					role_id=user_role.role_id,
					created_by=user_id
				)
				db.add(role_permission)
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new permissions to ROOT role (skipped existing ones)")

	except Exception as e:
		db.rollback()
		print(f"❌ Error assigning admin permissions: {str(e)}")
		raise
	finally:
		db.close()


def seed_loadable_items(user_id: str) -> None:
	"""Seeds loadable items from various CSV files."""
	db = get_db()
	try:
		items = get_loadable_items()

		added_count = 0
		for item in items:
			existing_item = db.query(LoadableItem).filter(
				LoadableItem.type == item['type'].upper(),
				LoadableItem.code == item['code'].upper()
			).first()

			if not existing_item:
				loadable_item = LoadableItem(
					type=item['type'].upper(),
					display_value=item['display_value'],
					code=item['code'].upper(),
					description=parse_description(item['description']),
					created_by=user_id
				)
				db.add(loadable_item)
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new loadable items (skipped existing ones)")
	except Exception as e:
		db.rollback()
		print(f"❌ Error seeding loadable items: {str(e)}")
		raise
	finally:
		db.close()


def seed_currencies(user_id: str) -> None:
	"""Seeds currency data from currencies.csv file."""
	db = get_db()
	try:
		data = pd.read_csv(get_file_path("currencies.csv"))
		currencies = validate_columns(data, ['name', 'code', 'exchange_rate', 'default'])

		added_count = 0
		for row in currencies:
			existing_currency = db.query(Currency).filter(Currency.code == row['code']).first()
			if not existing_currency:
				db.add(Currency(
					name=row['name'],
					code=row['code'],
					exchange_rate=row['exchange_rate'],
					is_default=row['default'] == 'Yes',
					created_by=user_id
				))
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new currencies (skipped existing ones)")
	except Exception as e:
		db.rollback()
		print(f"❌ Failed to seed currencies: {str(e)}")
		raise
	finally:
		db.close()


def seed_regions(user_id: str) -> None:
	"""Seeds region data from regions.csv file."""
	db = get_db()
	try:
		data = pd.read_csv(get_file_path("regions.csv"))
		regions = validate_columns(data, ['name', 'code', 'description'])

		added_count = 0
		for region_data in regions:
			existing_region = db.query(Region).filter(Region.code == region_data['code']).first()
			if not existing_region:
				db.add(Region(
					name=region_data['name'],
					code=region_data['code'],
					description=parse_description(region_data['description']),
					created_by=user_id
				))
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new regions (skipped existing ones)")
	except Exception as e:
		db.rollback()
		print(f"❌ Failed to seed regions: {str(e)}")
		raise
	finally:
		db.close()


def seed_districts(user_id: str) -> None:
	"""Seeds district data from districts.csv file."""
	db = get_db()
	try:
		data = pd.read_csv(get_file_path("districts.csv"))
		districts = validate_columns(data, ['name', 'code', 'region'])

		added_count = 0
		for row in districts:
			existing_district = db.query(District).filter(District.code == row['code']).first()
			if not existing_district:
				region = db.query(Region).filter(Region.code == row['region']).first()
				if region:
					db.add(District(
						name=row['name'],
						code=row['code'],
						region_id=region.id,
						created_by=user_id
					))
					added_count += 1
				else:
					print(f"⚠️ Region '{row['region']}' not found for district '{row['name']}'")

		db.commit()
		print(f"✅ Added {added_count} new districts (skipped existing ones)")
	except Exception as e:
		db.rollback()
		print(f"❌ Failed to seed districts: {str(e)}")
		raise
	finally:
		db.close()


def seed_roles(user_id: str) -> None:
	"""Seeds role data from roles.csv file."""
	db = get_db()
	try:
		data = pd.read_csv(get_file_path("roles.csv"))
		roles = validate_columns(data, ['name', 'code', 'description'])

		added_count = 0
		for role_data in roles:
			existing_role = db.query(Role).filter(Role.code == role_data['code']).first()
			if not existing_role:
				db.add(Role(
					name=role_data['name'],
					code=role_data['code'],
					description=parse_description(role_data['description']),
					created_by=user_id
				))
				added_count += 1

		db.commit()
		print(f"✅ Added {added_count} new roles (skipped existing ones)")

		update_admin_role(user_id)
	except Exception as e:
		db.rollback()
		print(f"❌ Failed to seed roles: {str(e)}")
		raise
	finally:
		db.close()


def run_seed() -> None:
	"""Runs the complete seeding process in the correct order."""
	try:
		seed_countries()
		create_root_account()

		user = get_root_user()
		if not user:
			print("❌ Could not get root user")
			return

		seed_currencies(user.id)
		seed_regions(user.id)
		seed_districts(user.id)
		seed_roles(user.id)
		seed_departments(user.id)
		seed_loadable_items(user.id)
		seed_role_permissions(user.id)

		print("✅ Database seeding completed successfully!")
	except Exception as e:
		print(f"❌ Database seeding failed: {str(e)}")


if __name__ == "__main__":
	run_seed()
