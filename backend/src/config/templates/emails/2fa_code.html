<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Secure Authentication - NGORA Portal</title>
    <style>
      :root {
        --color-primary: #6015a6;
        --color-secondary: #d25400;
        --color-accent: #4a90b8;
        --color-success: #38a169;
        --color-warning: #d69e2e;
        --color-danger: #e53e3e;
        --color-text: #2d3748;
        --color-text-light: #718096;
        --color-bg: #f7fafc;
        --color-bg-dark: #edf2f7;
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --gradient-primary: linear-gradient(135deg, #6015a6 0%, #d25400 100%);
        --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      * {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
        line-height: 1.6;
        background: var(--gradient-bg);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      body::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
            circle at 20% 50%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 80% 20%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 40% 80%,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%
          );
        pointer-events: none;
      }

      .container {
        max-width: 480px;
        width: 100%;
        padding: 20px;
        position: relative;
        z-index: 1;
      }

      .card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 24px;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        position: relative;
      }

      .card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
      }

      .header {
        background: var(--gradient-primary);
        padding: 40px 32px 32px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 1px,
          transparent 1px
        );
        background-size: 20px 20px;
        animation: float 20s infinite linear;
      }

      @keyframes float {
        0% {
          transform: translate(0, 0) rotate(0deg);
        }
        100% {
          transform: translate(-20px, -20px) rotate(360deg);
        }
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        position: relative;
        z-index: 2;
      }

      .logo-icon {
        width: 64px;
        height: 64px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .logo-icon::before {
        content: "🔐";
        font-size: 28px;
        filter: grayscale(1) brightness(0) invert(1);
      }

      .logo-text {
        color: white;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: -0.5px;
      }

      .gov-badge {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        padding: 6px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
      }

      .content {
        padding: 40px 32px;
        text-align: center;
      }

      .title {
        color: var(--color-primary);
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.5px;
      }

      .subtitle {
        color: var(--color-text-light);
        font-size: 16px;
        margin-bottom: 32px;
      }

      .user-greeting {
        background: var(--color-bg);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 32px;
        border-left: 4px solid var(--color-secondary);
      }

      .user-greeting p {
        margin: 0;
        color: var(--color-text);
        font-weight: 500;
      }

      .code-section {
        background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 20px;
        padding: 32px;
        margin: 32px 0;
        border: 1px solid rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
      }

      .code-section::before {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: var(--gradient-primary);
        border-radius: 22px;
        z-index: -1;
        opacity: 0.1;
      }

      .code-label {
        color: var(--color-text-light);
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 16px;
      }

      .verification-code {
        font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
          "Courier New", monospace;
        font-size: 36px;
        font-weight: 700;
        letter-spacing: 8px;
        color: var(--color-primary);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin: 0;
        padding: 16px;
        background: white;
        border-radius: 12px;
        border: 2px solid var(--color-bg-dark);
        transition: all 0.3s ease;
      }

      .verification-code:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .security-info {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(56, 161, 105, 0.1);
        border: 1px solid rgba(56, 161, 105, 0.2);
        border-radius: 12px;
        padding: 16px;
        margin: 24px 0;
        color: var(--color-success);
        font-size: 14px;
        font-weight: 500;
      }

      .security-info::before {
        content: "🛡️";
        margin-right: 8px;
        font-size: 16px;
      }

      .expiry-notice {
        background: rgba(214, 158, 46, 0.1);
        border: 1px solid rgba(214, 158, 46, 0.2);
        color: var(--color-warning);
        padding: 16px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 500;
        margin: 24px 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .expiry-notice::before {
        content: "⏰";
        margin-right: 8px;
        font-size: 16px;
      }

      .cta-button {
        display: inline-flex;
        align-items: center;
        background: var(--gradient-primary);
        color: white;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(43, 119, 184, 0.3);
        margin-top: 24px;
      }

      .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(43, 119, 184, 0.4);
      }

      .cta-button::after {
        content: "→";
        margin-left: 8px;
        font-size: 18px;
        transition: transform 0.3s ease;
      }

      .cta-button:hover::after {
        transform: translateX(4px);
      }

      .footer {
        background: var(--color-bg);
        padding: 24px 32px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        text-align: center;
      }

      .footer p {
        margin: 0;
        color: var(--color-text-light);
        font-size: 13px;
        line-height: 1.5;
      }

      .footer p:first-child {
        margin-bottom: 8px;
      }

      .warning-text {
        background: rgba(229, 62, 62, 0.1);
        border: 1px solid rgba(229, 62, 62, 0.2);
        color: var(--color-danger);
        padding: 16px;
        border-radius: 12px;
        font-size: 14px;
        margin: 24px 0;
        display: flex;
        align-items: flex-start;
      }

      .warning-text::before {
        content: "⚠️";
        margin-right: 8px;
        font-size: 16px;
        flex-shrink: 0;
        margin-top: 1px;
      }

      /* Responsive design */
      @media screen and (max-width: 540px) {
        .container {
          padding: 10px;
        }

        .header {
          padding: 32px 24px 24px;
        }

        .content {
          padding: 32px 24px;
        }

        .footer {
          padding: 20px 24px;
        }

        .verification-code {
          font-size: 28px;
          letter-spacing: 4px;
        }

        .logo-text {
          font-size: 24px;
        }

        .logo-icon {
          width: 56px;
          height: 56px;
        }
      }

      /* Accessibility */
      @media (prefers-reduced-motion: reduce) {
        .header::before {
          animation: none;
        }

        .verification-code:hover,
        .cta-button:hover {
          transform: none;
        }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        :root {
          --color-bg: #1a202c;
          --color-bg-dark: #2d3748;
          --color-text: #e2e8f0;
          --color-text-light: #a0aec0;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="card">
        <div class="header">
          <div class="logo">
            <div class="logo-icon"></div>
            <div class="logo-text">myNGO</div>
          </div>
          <div class="gov-badge">Official NGORA Portal</div>
        </div>

        <div class="content">
          <h1 class="title">Secure Authentication Required</h1>
          <p class="subtitle">Two-Factor Authentication Verification</p>

          <div class="user-greeting">
            <p>Hello, <strong>{{ username }}</strong></p>
          </div>

          <p style="color: var(--color-text-light); margin-bottom: 0">
            A secure login attempt has been detected for your government
            account. Please enter the verification code below to complete your
            authentication.
          </p>

          <div class="code-section">
            <div class="code-label">Your Verification Code</div>
            <div class="verification-code">{{code}}</div>
          </div>

          <div class="security-info">
            End-to-end encrypted and secure transmission
          </div>

          <div class="expiry-notice">
            Code expires in {{ expires_in_minutes }} minutes for security
          </div>

          <div class="warning-text">
            <div>
              If you did not initiate this login attempt, please contact your
              system administrator immediately and do not share this code with
              anyone.
            </div>
          </div>

          <a href="#" class="cta-button">Access Secure Portal</a>
        </div>

        <div class="footer">
          <p>
            This is an automated security notification. Do not reply to this
            message.
          </p>
          <p>&copy; {{ year }} NGORA. All rights reserved.</p>
        </div>
      </div>
    </div>
  </body>
</html>
