from fastapi import Depends, <PERSON>AP<PERSON>, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi_pagination import add_pagination

from src.config.routes import configure_routes
from src.config.settings import get_trusted_hosts
from src.core.shared_schema import BaseResponse


def create_app() -> FastAPI:
	app = FastAPI(
		title="myNGO API",
		description="API Documentation",
		version="1.0.0",
		debug=True
	)

	app.add_middleware(
		CORSMiddleware,
		allow_origins=get_trusted_hosts(),
		allow_credentials=True,
		allow_methods=["*"],
		allow_headers=["*"],
	)
 
	add_pagination(app)

	@app.get("/", include_in_schema=False)
	async def root():
		return RedirectResponse(url="/docs")

	@app.get("/health", tags=["system"])
	async def health_check():
		"""
		Comprehensive health check endpoint that monitors:
		- Database connectivity
		- System resources (CPU, memory, disk)
		- External service status
		- Application uptime
		"""
		from src.core.shared_schema import BaseResponse
		from src.modules.system.system_service import SystemService

		system_service = SystemService()
		try:
			health_data = system_service.get_health_status()

			return BaseResponse(
				data=health_data,
				success=True,
				errors=[]
			)
		except Exception as e:
			error_message = f"Health check failed: {str(e)}"
			return BaseResponse(
				data=None,
				success=False,
				errors=[{"message": error_message, "code": "health_check_error"}]
			)

	@app.exception_handler(RequestValidationError)
	async def validation_exception_handler(request: Request, exc: RequestValidationError):
		"""
		Handle validation errors and return a consistent response format
		"""
		error_messages = []
		for error in exc.errors():
			loc = error.get('loc', [])
			field = loc[-1] if len(loc) > 0 else 'unknown'
			msg = error.get('msg', 'Unknown validation error')

			error_messages.append({
				"message": f"{field}: {msg}",
				"code": "validation_error",
				"field": field,
				"details": error
			})

		response_data = {
			"data": None,
			"success": False,
			"errors": error_messages
		}

		return JSONResponse(
			status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
			content=response_data
		)

	@app.exception_handler(HTTPException)
	async def http_exception_handler(request: Request, exc: HTTPException):
		"""
		Handle HTTP exceptions and return a consistent response format
		"""
		response_data = {
			"data": None,
			"success": False,
			"errors": [{
				"message": str(exc.detail),
				"code": f"http_{exc.status_code}",
				"status_code": exc.status_code
			}]
		}

		return JSONResponse(
			status_code=exc.status_code,
			content=response_data
		)

	configure_routes(app)
	return app
