services:
  db:
    image: postgres:17
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_HOST: ${POSTGRES_HOST}
      POSTGRES_PORT: ${POSTGRES_PORT}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5455:5432"
    networks:
      - ngora_pipeline
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  minio:
    image: minio/minio:latest
    env_file:
      - .env
    command: server /data --console-address ":9001"
    ports:
      - "9020:9000"
      - "9021:9001"
    volumes:
      - minio_data:/data
    networks:
      - ngora_pipeline
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  myngo_api:
    build:
      context: .
      dockerfile: Dockerfile.Development
    env_file:
      - .env
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      minio:
        condition: service_healthy
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/.venv
    networks:
      - ngora_pipeline

volumes:
  postgres_data:
  minio_data:

networks:
  ngora_pipeline:
    driver: bridge
