[project]
name = "backend"
version = "0.1.0"
description = "myNGO - manage NGOs online is a platform for capturing, storing, processing and reporting of NGO activities"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.15.2",
    "fastapi-pagination>=0.13.3",
    "fastapi[standard]>=0.115.12",
    "mailjet-rest>=1.4.0",
    "minio>=7.2.15",
    "pandas>=2.2.3",
    "passlib>=1.7.4",
    "psutil>=7.0.0",
    "psycopg2>=2.9.10",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.0",
    "qrcode[pil]>=8.2",
    "reportlab>=4.4.0",
    "rich>=14.0.0",
    "sqlalchemy>=2.0.40",
    "sqlparse>=0.5.3",
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.5",
    "pytest-cov>=4.1.0",
    "httpx>=0.27.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_mode = "auto"
