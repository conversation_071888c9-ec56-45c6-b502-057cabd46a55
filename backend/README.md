# Ngora Backend

This is the backend service for the Ngora platform, designed to manage NGOs online. It provides robust APIs and background processing for capturing, storing, processing, and reporting on NGO activities. The backend is built with **FastAPI**, uses **PostgreSQL** as its database, is containerized with **Docker**, and dependency manager **uv** for Python.

---

## Table of Contents

- [Ngora Backend](#ngora-backend)
  - [Table of Contents](#table-of-contents)
  - [Features](#features)
  - [Tech Stack](#tech-stack)
  - [Prerequisites](#prerequisites)
  - [Getting Started](#getting-started)
    - [1. Clone the Repository](#1-clone-the-repository)
    - [2. Environment Configuration](#2-environment-configuration)
    - [3. Local Development](#3-local-development)
    - [4. Running with Docker Compose](#4-running-with-docker-compose)
    - [5. Database Migrations and Seeding](#5-database-migrations-and-seeding)
      - [Generating new Migrations:](#generating-new-migrations)
      - [Local Environment](#local-environment)
      - [Docker Environment](#docker-environment)
  - [Project Structure](#project-structure)
  - [Contributing](#contributing)
  - [License](#license)

---

## Features

- Modern, async RESTful API with FastAPI
- Secure JWT-based authentication
- Database integration with PostgreSQL and SQLAlchemy
- Easy environment management with `.env` files
- Containerized for reproducible production and development environments
- Email sending support
- Reporting and QR code generation utilities

---

## Tech Stack

- **Language:** Python 3.12+
- **Web Framework:** FastAPI
- **Database:** PostgreSQL
- **ORM:** SQLAlchemy
- **Migrations:** Alembic
- **Dependency Management:** [uv](https://github.com/astral-sh/uv)
- **Containerization:** Docker
- **Other:** Mailjet, qrcode, reportlab

---

## Prerequisites

- Docker & Docker Compose
- Python 3.12+ (for local non-Docker development)
- [uv](https://github.com/astral-sh/uv) (optional, for local development)
- PostgreSQL (if running outside Docker)

---

## Getting Started

### 1. Clone the Repository

```bash
git clone https://github.com/tumbati/ngora-monorepo.git
cd ngora-monorepo/backend
```

### 2. Environment Configuration

Copy `.env.example` to `.env` and adjust values as needed:

```bash
cp .env.example .env
```

**Key variables (see `.env.example` for all):**

- `POSTGRES_USER`, `POSTGRES_PASSWORD`, `POSTGRES_DB`, `POSTGRES_PORT` , `POSTGRES_HOST`, `DATABASE_URL`
- `ENVIRONMENT`
- `SECRET_KEY`, `ALGORITHM`

### 3. Local Development

**Install dependencies using uv:**

```bash
uv venv .venv
source .venv/bin/activate
uv sync
```

**Initialize Blank Database With Migrations**

```bash
uv run python src/cli.py db init
```

**Run the FastAPI server:**
```bash
uvicorn main:app --reload
```
The API will be available at [http://localhost:8000/docs](http://localhost:8000/docs).

### 4. Running with Docker Compose

The project provides a `docker-compose.yml` (located in the `backend/` directory) that spins up both the FastAPI backend and a PostgreSQL database with a single command.

**Start the backend and database:**
```bash
docker compose up --build
```
This will:
- Build the backend image using the provided Dockerfile.
- Start the FastAPI server with hot-reloading for development.
- Start a PostgreSQL database service with persistent storage.
- Use the `.env` file for environment variables.

**Access the API docs:** [http://localhost:8000/docs](http://localhost:8000/docs)

**Stop all services:**
```bash
docker compose down
```

### 5. Database Migrations and Seeding

The project includes built-in commands for database migrations and seeding. These can be run both locally and within Docker containers.

#### Generating new Migrations:

```bash
alembic revision --autogenerate -m "<migration-description>"
```


#### Local Environment

**Run database migrations:**

```bash
# Using the FastAPI CLI
python -m src.cli db migrate

# Or using Alembic directly
alembic upgrade head
```

**Seed the database with initial data:**

```bash
python -m src.cli db seed
```

#### Docker Environment

**Run database migrations in Docker:**

```bash
# For a running container
docker compose exec myngo_api python -m src.cli db migrate

# Or as a one-off command
docker compose run --rm myngo_api python -m src.cli db migrate
```

**Seed the database in Docker:**

```bash
# For a running container
docker compose exec myngo_api python -m src.cli db seed

# Or as a one-off command
docker compose run --rm myngo_api python -m src.cli db seed
```

**Note:** Database migrations should typically be run before starting your application or immediately after database schema changes. Seeding is usually performed once after initial setup.

---

## Project Structure

```
backend/
├── src/                      # Main FastAPI application code
├── alembic.ini               # Alembic configguration
├── main.py                   # Application entrypoint
├── pyproject.toml            # Project metadata and dependencies
├── Dockerfile                # Production docker setup
├── Dockerfile.Development    # Development docker setup
├── docker-compose.yml
├── .env.example
└── ...
```

---

## Contributing

We welcome contributions from both seasoned and aspiring developers! Please check the main repository's profile or the `CONTRIBUTING.md` guide for best practices, code style, and how to propose changes. All contributions are reviewed and tested for quality.

---

## License

Distributed under the MIT License. See `LICENSE` for more information.

---

**Need help?** Open an issue or start a discussion. Happy coding!