import os
import pytest
import async<PERSON>
from typing import As<PERSON><PERSON>enerator, Generator
from fastapi import FastAP<PERSON>
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from src.app import create_app
from src.config.db.database import get_db
from src.config.db.models.base import Base

# Test database URL - Use an in-memory SQLite for tests
TEST_DATABASE_URL = "sqlite:///./test.db"

# Create test engine and session
test_engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


# Using pytest-asyncio's built-in event_loop fixture instead of redefining it
# This avoids the deprecation warning


@pytest.fixture(scope="function")
def db() -> Generator[Session, None, None]:
    """
    Create a fresh database for each test.
    """
    # Create tables
    Base.metadata.create_all(bind=test_engine)
    
    # Create a new session for each test
    connection = test_engine.connect()
    transaction = connection.begin()
    session = TestSessionLocal(bind=connection)
    
    yield session
    
    # Clean up after the test
    session.close()
    transaction.rollback()
    connection.close()
    
    # Drop tables after test
    Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def override_get_db(db: Session):
    """
    Override the get_db dependency to use the test database.
    """
    def _get_test_db():
        try:
            yield db
        finally:
            pass
    return _get_test_db


@pytest.fixture
def app(override_get_db) -> FastAPI:
    """
    Create a FastAPI app for testing.
    """
    app = create_app()
    
    # Override the get_db dependency
    app.dependency_overrides[get_db] = override_get_db
    
    return app


@pytest.fixture
async def client(app: FastAPI) -> AsyncGenerator[AsyncClient, None]:
    """
    Create an async HTTP client for testing the FastAPI app.
    """
    # Start the FastAPI application in the background
    from fastapi.testclient import TestClient
    
    # Use TestClient for test request conversion
    async with AsyncClient(
        base_url="http://test", 
        follow_redirects=True
    ) as ac:
        # Override the app's router with our test app's router
        app_client = TestClient(app)
        
        # Patch the AsyncClient to send requests through the FastAPI test client
        original_request = ac.request
        
        async def patched_request(method, url, **kwargs):
            # Convert the AsyncClient request to a TestClient request
            url = str(url)
            if url.startswith("http://test"):
                url = url[len("http://test"):]
            
            # Use the TestClient to make the actual request to the FastAPI app
            response = app_client.request(method=method, url=url, **kwargs)
            
            # Convert the response back to an httpx Response
            return response
        
        # Replace the request method
        ac.request = patched_request
        
        yield ac


@pytest.fixture
def test_user_data():
    """
    Fixture to provide test user data.
    """
    return {
        "first_name": "Test",
        "last_name": "User",
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "Password123!"
    }

