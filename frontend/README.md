# myNGO Frontend

## Technologies Used

- [Next.js 14](https://nextjs.org/docs/getting-started)
- [HeroUI v2](https://heroui.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Tailwind Variants](https://tailwind-variants.org)
- [TypeScript](https://www.typescriptlang.org/)
- [Framer Motion](https://www.framer.com/motion/)
- [next-themes](https://github.com/pacocoursey/next-themes)

### Install dependencies

You can only use `pnpm`

```bash
pnpm install
```

Create .env file using .env.example template
```bash
cp .env.example .env
```

### Run the development server

```bash
pnpm run dev
```

## License

TUMBATI LICENCE
