import { UserFormData } from "@/components/users/user-schema.ts";
import api from "@/config/api.config.ts";
import http from "@/config/http.ts";
import { HttpResponse, UserDto, UserRequest } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common.ts";

export const fetchAll = async (
  filter: Partial<UserRequest>
): Promise<HttpResponse<UserDto[]>> => {
  try {
    if (filter.search) {
      filter.first_name = filter.search;
    }

    const url = `${api.v1.users.root}?${jsonToQueryParams(filter)}`;
    const response = await http.get(url);

    return httpResponse<UserDto[]>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};

export const createUser = async (
  data: UserFormData
): Promise<HttpResponse<UserDto>> => {
  try {
    const response = await http.post(api.v1.users.root, data);

    return httpResponse<UserDto>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};

export const updateUser = async (
  data: UserFormData
): Promise<HttpResponse<UserDto>> => {
  try {
    const response = await http.put(api.v1.users.root, data);

    return httpResponse<UserDto>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};

export const deleteUser = async (
  userId: string
): Promise<HttpResponse<boolean>> => {
  try {
    const response = await http.delete(api.v1.users.delete(userId));

    return httpResponse<boolean>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};
