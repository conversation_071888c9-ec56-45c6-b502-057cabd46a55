import api from "@/config/api.config";
import http from "@/config/http";
import { CountryDto, HttpResponse } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export async function fetchCountries(
  filter: Partial<CountryDto>
): Promise<HttpResponse<CountryDto[]>> {
  try {
    const response = await http.get(
      `${api.v1.loadable_items.countries}?${jsonToQueryParams(filter)}`
    );

    return httpResponse<CountryDto[]>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
}
