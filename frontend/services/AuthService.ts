import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, LoginResponse, LoginSchema } from "@/types";
import { httpResponse } from "@/utils/common";

export const login = async (
  data: LoginSchema
): Promise<HttpResponse<LoginResponse>> => {
  try {
    const form = new FormData();

    form.append("username", data.email);
    form.append("password", data.password);
    form.append("scope", "");
    form.append("client_id", "");
    form.append("client_secret", "");

    const response = await http.post(api.v1.auth.login, form, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });

    return httpResponse<LoginResponse>(response);
  } catch (error: any) {
    return httpResponse<LoginResponse>(error.response);
  }
};
