import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, Pagination, RoleDto, RoleFilter } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

type RoleRequest = {} & Pagination & RoleDto;

export const fetchAll = async (
  filter: Partial<RoleFilter>
): Promise<HttpResponse<RoleDto[]>> => {
  try {
    const url = `${api.v1.roles.root}?${jsonToQueryParams(filter)}`;
    const response = await http.get(url);

    return httpResponse<RoleDto[]>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};

export const createRole = async (
  filter: Partial<RoleRequest>
): Promise<HttpResponse<RoleDto[]>> => {
  try {
    const url = `${api.v1.roles.root}?${jsonToQueryParams(filter)}`;
    const response = await http.get(url);

    return httpResponse<RoleDto[]>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};

export const updateRole = async (
  data: Partial<RoleDto>
): Promise<HttpResponse<RoleDto>> => {
  try {
    const response = await http.put(api.v1.roles.root, data);

    return httpResponse<RoleDto>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};

export const deleteRole = async (
  roleId: string
): Promise<HttpResponse<boolean>> => {
  try {
    const response = await http.delete(api.v1.roles.delete(roleId));

    return httpResponse<boolean>(response);
  } catch (e: any) {
    return httpResponse(e.response);
  }
};
