"use client";

import { Ava<PERSON>, AvatarIcon } from "@heroui/avatar";
import { Button, ButtonGroup } from "@heroui/button";
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
} from "@heroui/dropdown";
import { cn } from "@heroui/theme";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import {
  AltArrowRightBold,
  BellBold,
  HomeAngleBold,
  Logo,
  Logout3Bold,
  SettingsBold,
  UserBold,
  UsersGroupRoundedBold
} from "./icons";

import { useAuth, useUI } from "@/composables/useStore";
import { siteConfig } from "@/config/site";

export const Sidebar = () => {
  const {
    sidebarExpanded: isExpanded,
    setSidebarExpanded: setIsExpanded,
    setCurrentPage,
  } = useUI();
  const { session } = useAuth();

  const [_, setIsMediumScreen] = useState(false);
  const router = useRouter();
  const currentPath = usePathname();

  useEffect(() => {
    const handleResize = () => {
      setIsMediumScreen(window.innerWidth < 1024 && window.innerWidth >= 768);
      if (window.innerWidth < 1024 && window.innerWidth >= 768) {
        setIsExpanded(false);
      } else if (window.innerWidth >= 1024) {
        setIsExpanded(true);
      }
    };

    handleResize();

    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const [selectedOption, setSelectedOption] = useState("");

  const navItems = [
    { icon: <HomeAngleBold />, label: "Dashboard", href: "/dashboard" },
    // { icon: <UserIdBold />, label: "Organizations", href: "/organizations" },
    { icon: <BellBold />, label: "Notifications", href: "/notifications" },
    { icon: <UsersGroupRoundedBold />, label: "Users", href: "/users" },
    { icon: <SettingsBold />, label: "Settings", href: "/settings" },
  ];

  useEffect(() => {
    const currentNavItem = navItems.find(
      (item) =>
        currentPath === item.href ||
        (item.href !== "/" && currentPath.startsWith(item.href)),
    );

    if (currentNavItem) {
      setCurrentPage(currentNavItem.label);
    } else if (currentPath === "/profile") {
      setCurrentPage("Profile");
    } else if (currentPath === "/login") {
      setCurrentPage("Login");
    } else {
      setCurrentPage("Dashboard");
    }
  }, [currentPath, setCurrentPage]);

  const selectedOptionValue = Array.from(selectedOption)[0];
  const iconClasses =
    "text-xl text-default-500 pointer-events-none flex-shrink-0";

  return (
    <div
      className={`will-change relative flex h-full flex-col gap-3 transition-all duration-300 items-center py-5 border-r dark:border-gray-800 bg-background shadow-sm ${
        isExpanded ? "w-64" : "w-16"
      }`}
    >
      <div className="flex items-center justify-left px-4 gap-3 w-full">
        <div className="flex flex-row gap-2">
          <Logo />
          {isExpanded && (
            <span className="text-lg uppercase font-bold truncate">
              {siteConfig.name}
            </span>
          )}
        </div>
      </div>

      <div className="flex flex-col justify-between w-full mt-6 space-y-2">
        {navItems.map((item, index) => {
          const isActive =
            currentPath === item.href ||
            (item.href !== "/" && currentPath.startsWith(item.href));

          return (
            <Button
              key={index}
              className={`w-full ${isExpanded ? "justify-start px-4" : "justify-center"}
								${isActive ? "bg-accent text-primary" : "hover:bg-accent"}`}
              isIconOnly={!isExpanded}
              variant="light"
              onPress={() => router.push(item.href)}
            >
              <span
                className={`flex-shrink-0 ${isActive ? "text-primary" : ""}`}
              >
                {item.icon}
              </span>
              {isExpanded && <span className="ml-3">{item.label}</span>}
            </Button>
          );
        })}
      </div>

      <div className="mt-auto w-full">
        <ButtonGroup variant="flat">
          <Dropdown placement="right-end">
            <DropdownTrigger>
              <Button
                className="w-full p-3"
                isIconOnly={!isExpanded}
                variant="light"
              >
                <div className="flex items-center justify-between gap-3 w-full">
                  <Avatar icon={<AvatarIcon />} size="sm" />
                  {isExpanded && (
                    <>
                      <span>@{session?.username}</span>
                      <AltArrowRightBold height="1em" />
                    </>
                  )}
                </div>
              </Button>
            </DropdownTrigger>
            <DropdownMenu
              disallowEmptySelection
              aria-label="Merge options"
              className="max-w-[300px]"
              selectedKeys={new Set([selectedOptionValue])}
              selectionMode="single"
              variant="faded"
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0] as string;

                setSelectedOption(selected);

                if (selected === "profile") {
                  router.push("/profile");
                } else if (selected === "logout") {
                  console.log("Logout triggered");
                }
              }}
            >
              <DropdownSection showDivider title="Actions">
                <DropdownItem
                  key="profile"
                  description={"@" + session?.username}
                  startContent={
                    <UserBold className={iconClasses} height="1.3em" />
                  }
                >
                  {session?.first_name} {session?.last_name}
                </DropdownItem>
              </DropdownSection>

              <DropdownSection title="Danger zone">
                <DropdownItem
                  key="logout"
                  description="Logout from the system"
                  startContent={
                    <Logout3Bold className={cn(iconClasses)} height="1.3em" />
                  }
                >
                  Logout
                </DropdownItem>
              </DropdownSection>
            </DropdownMenu>
          </Dropdown>
        </ButtonGroup>
      </div>
    </div>
  );
};
