import { z } from "zod";

export const userSchema = z.object({
  user_id: z.string().optional(),
  first_name: z.string().min(3, "First Name must be at least 3 characters"),
  middle_name: z.string().optional(),
  last_name: z.string().min(3, "Last Name must be at least 3 characters"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(8, "Enter a valid phone number"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  role_id: z.string().uuid("Role is required"),
});

export type UserFormData = z.infer<typeof userSchema>;
