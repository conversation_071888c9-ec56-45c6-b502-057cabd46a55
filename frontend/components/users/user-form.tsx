import { Autocomplete, AutocompleteItem } from "@heroui/autocomplete";
import { Button } from "@heroui/button";
import { Form } from "@heroui/form";
import { Input } from "@heroui/input";
import { addToast } from "@heroui/toast";
import { useEffect, useRef, useState } from "react";
import { z } from "zod";

import * as RoleService from "@/services/RoleService";
import * as UserService from "@/services/UserService";
import { RoleDto, UserDto } from "@/types";
import { UserFormData, userSchema } from "./user-schema";

type UserFormProps = {
  onClose: () => void;
  onAdd: (user: UserDto) => void;
  onUpdate: (user: UserDto) => void;
  user: UserDto | null;
  formMode: "create" | "edit";
};

export default function UserForm({
  onClose,
  onAdd,
  user,
  formMode,
  onUpdate,
}: UserFormProps) {
  const [formData, setFormData] = useState<UserFormData>({
    user_id: undefined,
    first_name: "",
    middle_name: "",
    last_name: "",
    username: "",
    email: "",
    phone: "",
    password: "",
    role_id: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingRole, setIsFetchingRole] = useState(false);
  const [roles, setRoles] = useState<RoleDto[]>([]);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        user_id: user.user_id || undefined,
        first_name: user.first_name || "",
        middle_name: user.middle_name || "",
        last_name: user.last_name || "",
        username: user.username || "",
        email: user.email || "",
        phone: user.phone || "",
        password: "temporayPasss",
        role_id: user.role_id || "",
      }));
    }
  }, [user]);

  const handleRoleInputChange = (value: string) => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    setIsFetchingRole(true);

    debounceTimeout.current = setTimeout(async () => {
      try {
        const results = await RoleService.fetchAll({ name: value });

        if (results.data) {
          setRoles(results.data);
        }
      } catch (error) {
        console.error("Error fetching roles:", error);
      } finally {
        setIsFetchingRole(false);
      }
    }, 300);
  };

  const validateForm = () => {
    try {
      userSchema.parse(formData);
      setErrors({});

      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors: Record<string, string> = {};

        error.errors.forEach((err) => {
          if (err.path[0]) {
            formattedErrors[err.path[0].toString()] = err.message;
          }
        });
        setErrors(formattedErrors);
      }

      return false;
    }
  };

  const handleChange = (field: keyof UserFormData, value: string = "") => {
    if (isSubmitting) {
      validateForm();
    }

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      const response =
        formMode === "create"
          ? await UserService.createUser(formData)
          : await UserService.updateUser(formData);

      if (response.success && response.data) {
        addToast({
          title: "Success",
          color: "success",
          description:
            formMode === "create"
              ? "User created successfully"
              : "User updated successfully",
        });

        (formMode === "create" ? onAdd : onUpdate)(response.data);

        onClose();
      } else {
        addToast({
          title: "Error",
          color: "danger",
          description:
            response.errors?.[0]?.message || "Failed to process request",
        });
      }
    } catch (error) {
      addToast({
        title: "Error",
        color: "danger",
        description: "An error occurred while processing user",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Form autoComplete="off" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col md:flex-row items-center gap-3">
          <Input
            errorMessage={errors.first_name}
            id="first_name"
            isInvalid={!!errors.first_name}
            label="First Name"
            value={formData.first_name || ""}
            onValueChange={(value) => handleChange("first_name", value)}
          />
          <Input
            errorMessage={errors.last_name}
            id="last_name"
            isInvalid={!!errors.last_name}
            label="Last Name"
            value={formData.last_name || ""}
            onValueChange={(value) => handleChange("last_name", value)}
          />
        </div>

        <Input
          errorMessage={errors.middle_name}
          id="middle_name"
          isInvalid={!!errors.middle_name}
          label="Middle Name"
          value={formData.middle_name || ""}
          onValueChange={(value) => handleChange("middle_name", value)}
        />

        <Input
          errorMessage={errors.username}
          id="username"
          isInvalid={!!errors.username}
          label="Username"
          value={formData.username || ""}
          onValueChange={(value) => handleChange("username", value)}
        />

        <Input
          errorMessage={errors.email}
          id="email"
          isInvalid={!!errors.email}
          label="Email"
          type="email"
          value={formData.email || ""}
          onValueChange={(value) => handleChange("email", value)}
        />

        <Input
          errorMessage={errors.phone}
          id="phone"
          isInvalid={!!errors.phone}
          label="Phone"
          type="tel"
          value={formData.phone || ""}
          onValueChange={(value) => handleChange("phone", value)}
        />

        <Input
          className={formMode === "edit" ? "hidden" : ""}
          errorMessage={errors.password}
          id="password"
          isInvalid={!!errors.password}
          label="Password"
          type="password"
          value={formData.password || ""}
          onValueChange={(value) => handleChange("password", value)}
        />

        <Autocomplete
          defaultItems={roles}
          defaultSelectedKey={user?.role_id || ""}
          errorMessage={errors.role_id}
          id="role_id"
          isInvalid={!!errors.role_id}
          isLoading={isFetchingRole}
          label="Role"
          value={formData.role_id || ""}
          onInputChange={handleRoleInputChange}
          onSelectionChange={(value) =>
            handleChange("role_id", value as string)
          }
        >
          {(item) => (
            <AutocompleteItem key={item.role_id} textValue={item.name}>
              <div className="flex items-center gap-4">
                <span className="font-lg text-default-900">{item.name}</span>
              </div>
            </AutocompleteItem>
          )}
        </Autocomplete>

        <div className="flex items-center justify-end gap-3 w-full p-3">
          <Button
            color="danger"
            type="button"
            variant="light"
            onPress={onClose}
          >
            Cancel
          </Button>
          <Button color="primary" isLoading={isLoading} type="submit">
            {formMode === "create" ? "Create User" : "Update User"}
          </Button>
        </div>
      </Form>
    </div>
  );
}
