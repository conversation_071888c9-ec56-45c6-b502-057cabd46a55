import { Button } from "@heroui/button";
import {
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownTrigger,
} from "@heroui/dropdown";
import { Pagination } from "@heroui/pagination";
import {
	Table,
	TableBody,
	TableCell,
	TableColumn,
	TableHeader,
	TableRow,
} from "@heroui/table";
import { EllipsisVertical, Pen, Trash } from "lucide-react";
import React, { useCallback } from "react";

import { UserDto } from "@/types";

type UserTableProps = {
  page: number;
  pages: number;
  users: UserDto[];
  onPage: (page: number) => void;
  onEdit: (user: UserDto) => void;
  onDelete: (user: UserDto) => void;
  isLoading: boolean;
};

export default function UsersTable({
  users,
  pages,
  page,
  onPage,
  isLoading,
  onEdit,
  onDelete,
}: UserTableProps) {
  const renderCell = useCallback((user: UserDto, columnKey: React.Key) => {
    if (columnKey === "role") {
      return user.role?.name;
    }

    if (columnKey === "name") {
      return `${user.first_name} ${user.middle_name ? user.middle_name : ""} ${user.last_name}`;
    }

    if (columnKey === "phone") {
      return user.phone ? user.phone : "N/A";
    }

    if (columnKey === "actions") {
      return (
        <Dropdown>
          <DropdownTrigger>
            <Button
              isIconOnly
              startContent={<EllipsisVertical />}
              variant="light"
            />
          </DropdownTrigger>
          <DropdownMenu aria-label="Static Actions">
            <DropdownItem
              key="edit"
              textValue="edit"
              onPress={() => onEdit(user)}
            >
              <div className="flex items-center gap-3">
                <Pen size={16} />
                <span>Edit user</span>
              </div>
            </DropdownItem>
            <DropdownItem
              key="delete"
              className="text-danger"
              color="danger"
              textValue="delete"
              onPress={() => onDelete(user)}
            >
              <div className="flex items-center gap-3">
                <Trash size={16} />
                <span>Delete user</span>
              </div>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      );
    }

    return user[columnKey as keyof Omit<UserDto, "role">];
  }, []);

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Phone",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Role",
      dataIndex: "role",
      key: "role",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
    },
  ];

  return (
    <Table
      removeWrapper
      aria-label="Example table with custom cells"
      bottomContent={
        <div className="flex w-full justify-center">
          <Pagination
            isCompact
            showControls
            showShadow
            color="secondary"
            page={page}
            total={pages}
            onChange={(page) => onPage(page)}
          />
        </div>
      }
      classNames={{
        base: "p-0",
        table: "p-0 h-full",
        wrapper: "p-0 h-full",
      }}
      shadow="none"
    >
      <TableHeader columns={columns}>
        {(column) => (
          <TableColumn
            key={column.dataIndex}
            align={column.key === "actions" ? "center" : "start"}
          >
            {column.title}
          </TableColumn>
        )}
      </TableHeader>
      <TableBody isLoading={isLoading} items={users}>
        {(item) => (
          <TableRow key={item.user_id}>
            {(columnKey) => (
              <TableCell>
                <div className="py-1 font-semibold">
                  {renderCell(item, columnKey)}
                </div>
              </TableCell>
            )}
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
