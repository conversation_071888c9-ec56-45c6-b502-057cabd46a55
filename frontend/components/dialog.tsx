import { Button } from "@heroui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, useDisclosure } from "@heroui/modal";
import { useEffect } from "react";

export type DialogSelection = 'denied' | 'accepted'

type DialogProps = {
	title: string
	description: string
	acceptLabel?: string
	denyLabel?: string
	onSelection: (state: DialogSelection) => void
	show: boolean
}

export function Dialog({ show, title, description, onSelection, ...props }: DialogProps) {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();

	useEffect(() => {
		if (show) {
			onOpen()
		}
	}, [show])

	function setSelection(state: DialogSelection) {
		onSelection(state)
		onOpenChange()
	}

	return (
		<Modal
			isDismissable={false}
			isKeyboardDismissDisabled={true}
			isOpen={isOpen}
			onOpenChange={onOpenChange}
		>
			<ModalContent>
				{() => (
					<>
						<ModalHeader className="flex flex-col gap-1">{title}</ModalHeader>
						<ModalBody>
							<p>{description}</p>
						</ModalBody>
						<ModalFooter>
							<Button color="danger" variant="light" onPress={() => setSelection('denied')}>
								{props.denyLabel || 'No'}
							</Button>
							<Button color="primary" onPress={() => setSelection('accepted')}>
								{props.acceptLabel || "Yes"}
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
}