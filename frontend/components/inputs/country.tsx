import { Autocomplete, AutocompleteItem } from "@heroui/autocomplete";
import { Spinner } from "@heroui/spinner";
import { Key, useRef, useState } from "react";

import { fetchCountries } from "@/services/LoadableItemService";
import { CountryDto } from "@/types";

interface AsyncAutocompleteProps {
  className?: string;
  label?: string;
  placeholder?: string;
  onSelectionChange: (e: Key | null) => void;
  value?: string;
  isDisabled?: boolean;
  isRequired?: boolean;
  errorMessage?: string;
  isInvalid?: boolean;
}

export default function CountryInput({ ...props }: AsyncAutocompleteProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [countries, setCountries] = useState<CountryDto[]>([]);
  const debounceTimeout = useRef<NodeJS.Timeout>();

  const handleInputChange = (value: string) => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    setIsLoading(true);

    debounceTimeout.current = setTimeout(async () => {
      try {
        const results = await fetchCountries({ name: value });

        if (results.data) {
          setCountries(results.data);
        }
      } catch (error) {
        console.error("Error fetching countries:", error);
      } finally {
        setIsLoading(false);
      }
    }, 300);
  };

  return (
    <Autocomplete
      className={props.className}
      defaultItems={countries}
      endContent={isLoading && <Spinner size="sm" />}
      errorMessage={props.errorMessage}
      isDisabled={props.isDisabled}
      isInvalid={props.isInvalid}
      isRequired={props.isRequired}
      label={props.label}
      placeholder={props.placeholder}
      value={props.value}
      onInputChange={handleInputChange}
      onSelectionChange={props.onSelectionChange}
    >
      {(item) => (
        <AutocompleteItem key={item.country_id} textValue={item.name}>
          <div className="flex items-center gap-4">
            <span className="text-xl">{item.flag}</span>
            <span className="font-lg text-default-900">{item.name}</span>
          </div>
        </AutocompleteItem>
      )}
    </Autocomplete>
  );
}
