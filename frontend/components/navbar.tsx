"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import {
  Navbar as HeroUINavbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenuToggle,
} from "@heroui/navbar";

import { BarsIcon } from "./icons";

import { ThemeSwitch } from "@/components/theme-switch";
import { useUI } from "@/composables/useStore";

export const Navbar = () => {
  const { toggleSidebar, currentPage } = useUI();

  return (
    <HeroUINavbar
      className="border-b dark:border-gray-800"
      maxWidth="xl"
      position="sticky"
    >
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand className="gap-3 max-w-fit">
          <Button isIconOnly variant="light" onPress={(_) => toggleSidebar()}>
            <BarsIcon height="1.5em" />
          </Button>
          <p className="font-bold text-lg">{currentPage}</p>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent
        className="hidden sm:flex basis-1/5 sm:basis-full"
        justify="end"
      >
        <NavbarItem className="hidden sm:flex gap-2">
          <ThemeSwitch />
          {/* <Avatar
            classNames={{
              base: "bg-gradient-to-br from-[#FFB457] to-[#FF705B]",
              icon: "text-black/80",
            }}
            icon={<AvatarIcon />}
          /> */}
        </NavbarItem>
      </NavbarContent>

      <NavbarContent className="sm:hidden basis-1 pl-4" justify="end">
        <ThemeSwitch />
        <NavbarMenuToggle />
      </NavbarContent>
    </HeroUINavbar>
  );
};
