import axios from "axios";

import api from "./api.config";

import {
  getAccessToken,
  getRefreshToken,
  startSession,
} from "@/services/SessionService";

const axiosInstance = axios.create();

let navigateFunction: ((arg0: string) => void) | null = null;

export const setNavigateFunction = (
  navigate: ((arg0: string) => void) | null
) => {
  navigateFunction = navigate;
};

axiosInstance.interceptors.request.use((request) => {
  const token = getAccessToken();

  const modifiedHeaders = {
    withCredentials: true,
    "Content-Type": "application/json",
    authorization: `Bearer ${token}`,
    ...request.headers, // Allow overwrite on ['withCredentials', 'authorization', 'Content-Type']
  };

  Object.assign(request.headers, {
    ...modifiedHeaders,
  });

  return request;
});

axiosInstance.interceptors.response.use(
  (response) => response,
  async function (error) {
    const originalRequest = error.config;

    if (
      error.response?.status === 401 &&
      originalRequest.url === api.v1.auth.refreshToken
    ) {
      if (navigateFunction) {
        navigateFunction("/auth");
      } else {
        window.location.href = "/auth";
      }

      return Promise.reject(error);
    }

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        const response = await axiosInstance.post(api.v1.auth.refreshToken, {
          refresh_token: refreshToken,
        });

        if (response.status === 201) {
          startSession(response.data);
          axiosInstance.defaults.headers.common["Authorization"] =
            `Bearer ${getAccessToken()}`;

          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        if (navigateFunction) {
          navigateFunction("/auth");
        } else {
          window.location.href = "/auth";
        }

        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
