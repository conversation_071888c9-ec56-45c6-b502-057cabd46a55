const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

const v1 = `${baseUrl}/v1`;

const api = {
  v1: {
    auth: {
      login: v1 + "/auth/login",
      refreshToken: v1 + "/auth/refresh-token",
    },
    roles: {
      root: v1 + "/roles",
      delete: (id: string) => `${v1}/roles/${id}`,
    },
    loadable_items: {
      root: v1 + "/loadable_items",
      countries: v1 + "/loadable_items/countries",
      licence_fees: {
        root: v1 + "/loadable_items/licence_fees",
        delete: (id: string) => `${v1}/loadable_items/licence_fees/${id}`,
      },
    },
    users: {
      root: v1 + "/users",
      delete: (id: string) => `${v1}/users/${id}`,
    },
    workflows: {
      root: v1 + "/workflows",
      delete: (id: string) => `${v1}/workflows/${id}`,
      templates: {
        root: v1 + "/workflows/templates",
        stages: v1 + "/workflows/stages",
        roles: v1 + "/workflows/roles",
      },
    },
  },
};

export default api;
