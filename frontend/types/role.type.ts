import { Pagination } from "./common.type";

export interface RoleDto {
  role_id: string;
  name: string;
  description: string;
  created_at: Date;
  updated_at: Date;
}

export type RoleFilter = Partial<{} & Pagination & RoleDto>;

export interface RolePermissionDto {
  role_permission_id: string;
  role_id: string;
  permission_id: string;
  permission: string | null;
  created_at: Date;
  updated_at: Date;
}

export type RolePermissionFilterDto = RolePermissionDto & Pagination;
