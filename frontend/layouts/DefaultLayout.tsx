"use client";

import { ReactNode } from "react";

import { Navbar } from "@/components/navbar";
import { Sidebar } from "@/components/sidebar";
import { ThemeProvider } from "@/contexts/ThemeProvider";
import ProtectedRoute from "@/guards/ProtectedRoute";

interface Props {
  children: ReactNode;
}

export default function DefaultLayout({ children }: Props) {
  return (
    <ProtectedRoute
      node={
        <ThemeProvider>
          <div className="relative flex min-h-dvh flex-col max-w-7xl mx-auto">
            <div className="flex w-full">
              <div className="sticky top-0 h-screen">
                <Sidebar />
              </div>

              <main className="flex-grow flex flex-col">
                <div className="sticky top-0 z-10 bg-transparent">
                  <Navbar />
                </div>

                <div className="container mx-auto max-w-7xl overflow-y-auto">
                  {children}
                </div>
              </main>
            </div>
          </div>
        </ThemeProvider>
      }
    />
  );
}
