"use client";

import { ReactNode } from "react";

import { ThemeProvider } from "@/contexts/ThemeProvider";

export interface AuthLayoutProps {
  children: ReactNode;
}

export default function BlankLayout({ children }: AuthLayoutProps) {
  return (
    <ThemeProvider>
      <div className="relative flex flex-col h-screen">
        <main className="">{children}</main>
      </div>
    </ThemeProvider>
  );
}
