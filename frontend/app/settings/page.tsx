"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import {
  ArrowRight,
  Users,
  Workflow
} from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import React, { ReactNode } from "react";

type CardProps = {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
};

const SettingsCard = ({ title, description, icon, onClick }: CardProps) => {
  return (
    <div
      className="relative bg-white dark:bg-gray-900 rounded-xl border dark:border-gray-800 border-gray-200 p-6 shadow-sm transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer group"
      role="button"
      tabIndex={0}
      onClick={onClick}
      onKeyDown={() => ({})}
    >
      <div className="flex justify-between items-start mb-4">
        <div className="p-3 bg-primary-50 dark:bg-gray-800 rounded-lg text-primary-600">
          {icon}
        </div>
      </div>
      <h3 className="text-lg font-medium dark:text-gray-100 text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-gray-500 text-sm mb-4">{description}</p>
      <div className="flex items-center text-primary-600">
        <span className="mr-2 text-sm font-medium">View details</span>
        <ArrowRight
          className="transition-transform duration-300 transform group-hover:translate-x-2"
          size={16}
        />
      </div>
    </div>
  );
};

interface Props {
  children: ReactNode;
}

export default function SettingsIndexPage({ children }: Props) {
  const router = useRouter();
  const location = usePathname();
  const isBasePath = location === "/settings";

  const tabsData = [
    {
      id: "roles",
      label: "Roles",
      icon: <Users size={24} />,
      description: "Manage user roles and permissions within the system",
      path: "/settings/roles",
    },
    {
      id: "workflow",
      label: "Workflows",
      icon: <Workflow size={24} />,
      description: "Define process flows for different business operations",
      path: "/settings/workflows",
    },
  ];

  return (
    <div className="flex w-full flex-col p-6 overflow-x-hidden">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Settings</h1>
        <p className="text-gray-500">
          Manage your system configuration and preferences
        </p>
      </div>

      {!isBasePath && (
        <div>
          <Button
            className="mb-6 flex items-center text-blue-600 font-medium"
            variant="light"
            onPress={() => router.push("/settings")}
          >
            <ArrowRight className="mr-2 rotate-180" size={16} />
            <span className="font-bold">Back</span>
          </Button>
        </div>
      )}

      {isBasePath ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tabsData.map((tab) => (
            <SettingsCard
              key={tab.id}
              description={tab.description}
              icon={tab.icon}
              title={tab.label}
              onClick={() => router.push(tab.path)}
            />
          ))}
        </div>
      ) : (
        <div>{children}</div>
      )}
    </div>
  );
}
