"use client";

import {
  ArrowRight,
  Building,
  FileText,
  Filter,
  Search,
  Target,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

type Filter = {
  id: string;
  label: string;
  icon: any;
};

export default function Home() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);

  const handleFilterToggle = (filter: string) => {
    if (selectedFilters.includes(filter)) {
      setSelectedFilters(selectedFilters.filter((f) => f !== filter));
    } else {
      setSelectedFilters([...selectedFilters, filter]);
    }
  };

  const removeFilter = (filter: string) => {
    setSelectedFilters(selectedFilters.filter((f) => f !== filter));
  };

  const filterOptions: Filter[] = [
    { id: "ngo", label: "NGO", icon: <Users size={16} /> },
    { id: "cbo", label: "CBO", icon: <Users size={16} /> },
    {
      id: "international",
      label: "International",
      icon: <Building size={16} />,
    },
    { id: "local", label: "Local", icon: <Building size={16} /> },
    { id: "education", label: "Education", icon: <FileText size={16} /> },
    { id: "health", label: "Health", icon: <FileText size={16} /> },
    { id: "environment", label: "Environment", icon: <FileText size={16} /> },
    { id: "children", label: "Children", icon: <Target size={16} /> },
    { id: "women", label: "Women", icon: <Target size={16} /> },
    { id: "elderly", label: "Elderly", icon: <Target size={16} /> },
  ];

  return (
    <div className="min-h-screen dark:bg-gray-900 dark:text-gray-100 bg-gray-50 text-gray-900">
      {/* Header */}
      <header className="backdrop-blur-lg bg-white/70 dark:bg-gray-900/80 sticky top-0 z-50 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-violet-600 to-indigo-600 flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-xl">N</span>
                </div>
                <div>
                  <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-600 to-indigo-500">
                    myNGO
                  </div>
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    Non-Governmental Regulatory Authority
                  </span>
                </div>
              </div>
            </div>

            <nav className="hidden md:flex space-x-8">
              <Link
                className="font-medium text-gray-600 hover:text-violet-600 dark:text-gray-300 dark:hover:text-white"
                href="#"
              >
                Home
              </Link>
              <Link
                className="font-medium text-gray-600 hover:text-violet-600 dark:text-gray-300 dark:hover:text-white"
                href="#"
              >
                About
              </Link>
              <Link
                className="font-medium text-gray-600 hover:text-violet-600 dark:text-gray-300 dark:hover:text-white"
                href="#"
              >
                Organizations
              </Link>
              <Link
                className="font-medium text-gray-600 hover:text-violet-600 dark:text-gray-300 dark:hover:text-white"
                href="#"
              >
                Resources
              </Link>
              <Link
                className="font-medium text-gray-600 hover:text-violet-600 dark:text-gray-300 dark:hover:text-white"
                href="#"
              >
                Contact
              </Link>
            </nav>

            <div className="flex gap-3 items-center">
              <Link
                className="font-medium text-gray-600 hover:text-violet-600 dark:text-gray-300 dark:hover:text-white"
                href="/auth"
              >
                Login
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background with gradient and mesh */}
        <div className="absolute inset-0 bg-gradient-to-br from-violet-600 via-indigo-500 to-purple-700">
          <div className="absolute inset-0 bg-[url('/api/placeholder/1200/800')] mix-blend-overlay opacity-10" />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/30" />
        </div>

        {/* Circular blurs for modern effect */}
        <div className="absolute top-20 left-20 h-64 w-64 rounded-full bg-pink-500/30 blur-3xl" />
        <div className="absolute bottom-10 right-10 h-96 w-96 rounded-full bg-blue-500/20 blur-3xl" />

        <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold text-white tracking-tight">
              Find and Connect with{" "}
              <span className="inline-block bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-orange-500">
                Organizations
              </span>
            </h1>
            <p className="mt-6 text-xl md:text-2xl max-w-2xl mx-auto text-purple-100">
              Search our comprehensive database of registered non-governmental
              organizations operating across various sectors and locations.
            </p>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8 sm:-mt-16 mb-16 relative z-10">
        <div className="rounded-2xl shadow-2xl p-6 backdrop-blur-xl bg-white/90 dark:bg-gray-800/70 border border-gray-100 dark:border-gray-700 shadow-gray-200/40 dark:shadow-gray-900/40">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </div>
              <input
                className="block w-full pl-11 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-violet-500 border-0 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                placeholder="Search by organization name, registration number..."
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="relative">
              <button
                className="w-full md:w-auto flex items-center justify-center gap-2 px-6 py-3 rounded-xl font-medium transition-all bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-white"
                onClick={() => setIsFilterMenuOpen(!isFilterMenuOpen)}
              >
                <Filter className="h-5 w-5" />
                <span>Filter</span>
              </button>

              {isFilterMenuOpen && (
                <div className="absolute right-0 mt-2 p-5 rounded-xl shadow-xl z-10 w-72 bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      Filter By
                    </h3>
                    <button
                      className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400"
                      onClick={() => setIsFilterMenuOpen(false)}
                    >
                      <X size={16} />
                    </button>
                  </div>

                  {/* Organization Type */}
                  <div className="mb-5">
                    <h4 className="text-sm font-medium mb-3 text-gray-500 dark:text-gray-300">
                      Organization Type
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {filterOptions.slice(0, 4).map((option) => {
                        const isSelected = selectedFilters.includes(option.id);

                        return (
                          <div
                            key={option.id}
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer
                        ${
                          isSelected
                            ? "bg-violet-100 text-violet-800 dark:bg-violet-900/50 dark:text-violet-200"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                        }`}
                            role="button"
                            tabIndex={0}
                            onClick={() => handleFilterToggle(option.id)}
                            onKeyDown={() => ({})}
                          >
                            {option.icon}
                            <span className="text-sm">{option.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Sectors */}
                  <div className="mb-5">
                    <h4 className="text-sm font-medium mb-3 text-gray-500 dark:text-gray-300">
                      Sectors
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {filterOptions.slice(4, 7).map((option) => {
                        const isSelected = selectedFilters.includes(option.id);

                        return (
                          <div
                            key={option.id}
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer
                        ${
                          isSelected
                            ? "bg-violet-100 text-violet-800 dark:bg-violet-900/50 dark:text-violet-200"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                        }`}
                            role="button"
                            tabIndex={0}
                            onClick={() => handleFilterToggle(option.id)}
                            onKeyDown={() => ({})}
                          >
                            {option.icon}
                            <span className="text-sm">{option.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Target Groups */}
                  <div>
                    <h4 className="text-sm font-medium mb-3 text-gray-500 dark:text-gray-300">
                      Target Groups
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {filterOptions.slice(7).map((option) => {
                        const isSelected = selectedFilters.includes(option.id);

                        return (
                          <div
                            key={option.id}
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer
                        ${
                          isSelected
                            ? "bg-violet-100 text-violet-800 dark:bg-violet-900/50 dark:text-violet-200"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                        }`}
                            role="button"
                            tabIndex={0}
                            onClick={() => handleFilterToggle(option.id)}
                            onKeyDown={() => ({})}
                          >
                            {option.icon}
                            <span className="text-sm">{option.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <button className="px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl hover:shadow-violet-500/20">
              Search
            </button>
          </div>

          {/* Selected Filters */}
          {selectedFilters.length > 0 && (
            <div className="mt-4 flex flex-wrap gap-2">
              {selectedFilters.map((filter) => {
                const filterOption = filterOptions.find(
                  (opt) => opt.id === filter,
                );

                return (
                  <div
                    key={filter}
                    className="flex items-center rounded-full px-3 py-1 text-sm bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-200"
                  >
                    {filterOption?.icon}
                    <span className="ml-1">{filterOption?.label}</span>
                    <button
                      className="ml-1 text-violet-600 hover:text-violet-800 dark:text-violet-300 dark:hover:text-white"
                      onClick={() => removeFilter(filter)}
                    >
                      <X size={14} />
                    </button>
                  </div>
                );
              })}
              <button
                className="text-sm underline text-violet-600 hover:text-violet-800 dark:text-violet-400 dark:hover:text-violet-300"
                onClick={() => setSelectedFilters([])}
              >
                Clear all
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Featured Organizations */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="flex justify-between items-end mb-10">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Featured Organizations
            </h2>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Discover prominent organizations making an impact
            </p>
          </div>
          <Link
            className="flex items-center font-medium text-violet-600 hover:text-violet-800 dark:text-violet-400 dark:hover:text-violet-300"
            href="#"
          >
            View all
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Card 1 */}
          <div className="rounded-2xl overflow-hidden transform transition-all duration-300 hover:scale-[1.02] bg-white border border-gray-100 hover:shadow-xl hover:shadow-blue-200/50 dark:bg-gray-800 dark:border-gray-700 dark:hover:shadow-lg dark:hover:shadow-blue-600/10">
            <div className="h-2 bg-gradient-to-r from-blue-400 to-blue-600" />
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-bold text-lg text-gray-900 dark:text-white">
                    Global Health Initiative
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Reg. No: NGO-2023-1254
                  </p>
                </div>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Verified
                </span>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className="text-xs px-2.5 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200">
                  International
                </span>
                <span className="text-xs px-2.5 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200">
                  Health
                </span>
                <span className="text-xs px-2.5 py-1 rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200">
                  Children
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Working to improve healthcare access in underserved communities
                with a focus on preventative care and education.
              </p>
              <div className="mt-4">
                <Link
                  className="inline-flex items-center font-medium text-sm text-violet-600 hover:text-violet-800 dark:text-violet-400 dark:hover:text-violet-300"
                  href="#"
                >
                  View details
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Card 2 */}
          <div className="rounded-2xl overflow-hidden transform transition-all duration-300 hover:scale-[1.02] bg-white border border-gray-100 hover:shadow-xl hover:shadow-green-200/50 dark:bg-gray-800 dark:border-gray-700 dark:hover:shadow-lg dark:hover:shadow-green-600/10">
            <div className="h-2 bg-gradient-to-r from-green-400 to-green-600" />
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-bold text-lg text-gray-900 dark:text-white">
                    Education For All
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Reg. No: NGO-2022-0876
                  </p>
                </div>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Verified
                </span>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className="text-xs px-2.5 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200">
                  NGO
                </span>
                <span className="text-xs px-2.5 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200">
                  Education
                </span>
                <span className="text-xs px-2.5 py-1 rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-200">
                  Children
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Dedicated to ensuring quality education for underprivileged
                children through innovative teaching methods and resources.
              </p>
              <div className="mt-4">
                <Link
                  className="inline-flex items-center font-medium text-sm text-violet-600 hover:text-violet-800 dark:text-violet-400 dark:hover:text-violet-300"
                  href="#"
                >
                  View details
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>

          {/* Card 3 */}
          <div className="rounded-2xl overflow-hidden transform transition-all duration-300 hover:scale-[1.02] bg-white border border-gray-100 hover:shadow-xl hover:shadow-purple-200/50 dark:bg-gray-800 dark:border-gray-700 dark:hover:shadow-lg dark:hover:shadow-purple-600/10">
            <div className="h-2 bg-gradient-to-r from-purple-400 to-purple-600" />
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-bold text-lg text-gray-900 dark:text-white">
                    Women Empowerment Network
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Reg. No: CBO-2021-5432
                  </p>
                </div>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Verified
                </span>
              </div>

              <div className="flex flex-wrap gap-2 mb-4">
                <span className="text-xs px-2.5 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200">
                  CBO
                </span>
                <span className="text-xs px-2.5 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-200">
                  Education
                </span>
                <span className="text-xs px-2.5 py-1 rounded-full bg-pink-100 text-pink-800 dark:bg-pink-900/40 dark:text-pink-200">
                  Women
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Supporting women through skills development, entrepreneurship
                training, and advocacy for gender equality.
              </p>
              <div className="mt-4">
                <Link
                  className="inline-flex items-center font-medium text-sm text-violet-600 hover:text-violet-800 dark:text-violet-400 dark:hover:text-violet-300"
                  href="#"
                >
                  View details
                  <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-20 dark:bg-gray-800/50 bg-gray-50">
        <div className="relative">
          {/* Decorative blurs */}
          <div className="absolute top-0 left-1/4 h-64 w-64 rounded-full bg-violet-600/10 blur-3xl" />
          <div className="absolute bottom-0 right-1/4 h-64 w-64 rounded-full bg-indigo-600/10 blur-3xl" />

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-center text-3xl font-bold mb-16 dark:text-white text-gray-900">
              Our Impact in Numbers
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="p-6 rounded-2xl dark:bg-gray-800/70 dark:border-gray-700 bg-white/80 border border-gray-100 backdrop-blur-lg shadow-lg">
                <p className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                  5,280+
                </p>
                <p className="mt-2 dark:text-gray-300 text-gray-600">
                  Registered Organizations
                </p>
              </div>
              <div className="p-6 rounded-2xl dark:bg-gray-800/70 dark:border-gray-700 bg-white/80 border border-gray-100 backdrop-blur-lg shadow-lg">
                <p className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                  120+
                </p>
                <p className="mt-2 dark:text-gray-300 text-gray-600">
                  Countries Reached
                </p>
              </div>
              <div className="p-6 rounded-2xl dark:bg-gray-800/70 dark:border-gray-700 bg-white/80 border border-gray-100 backdrop-blur-lg shadow-lg">
                <p className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                  25+
                </p>
                <p className="mt-2 dark:text-gray-300 text-gray-600">
                  Sectors Covered
                </p>
              </div>
              <div className="p-6 rounded-2xl dark:bg-gray-800/70 dark:border-gray-700 bg-white/80 border border-gray-100 backdrop-blur-lg shadow-lg">
                <p className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                  10M+
                </p>
                <p className="mt-2 dark:text-gray-300 text-gray-600">
                  Beneficiaries
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="relative overflow-hidden py-20 dark:bg-gradient-to-br dark:from-violet-900/20 dark:to-indigo-900/20 bg-gradient-to-br from-violet-50 to-indigo-100">
        <div className="absolute inset-0 bg-gradient-to-br dark:from-violet-900/20 dark:to-indigo-900/20 from-violet-50 to-indigo-100" />

        <div className="absolute -top-24 -right-24 h-96 w-96 rounded-full bg-violet-500/10 blur-3xl" />
        <div className="absolute -bottom-24 -left-24 h-96 w-96 rounded-full bg-indigo-500/10 blur-3xl" />

        <div className="relative max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold dark:text-white text-gray-900">
            Ready to Register Your Organization?
          </h2>
          <p className="mt-4 text-lg max-w-2xl mx-auto dark:text-gray-300 text-gray-600">
            Join our database and increase your organization&apos;s visibility
            and collaboration opportunities.
          </p>
          <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
            <Link
              className="inline-flex justify-center items-center px-6 py-3 rounded-xl font-medium text-white bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 shadow-lg hover:shadow-xl hover:shadow-violet-500/20 transition-all duration-300"
              href="#"
            >
              Register Now
            </Link>
            <Link
              className="inline-flex justify-center items-center px-6 py-3 rounded-xl font-medium border dark:bg-gray-800 dark:text-white dark:border-gray-700 dark:hover:bg-gray-700 bg-white text-gray-700 border-gray-200 hover:bg-gray-50 transition-all duration-300"
              href="#"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="dark:bg-gray-900 bg-gray-800 border-t dark:border-gray-800 text-gray-300">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-violet-600 to-indigo-600 flex items-center justify-center mr-3">
                  <span className="text-white font-bold text-xl">N</span>
                </div>
                <div className="text-xl font-bold text-white">NGORA</div>
              </div>
              <p className="mt-4 text-sm text-gray-400">
                The Non-Governmental Regulatory Authority oversees and supports
                organizations working for social impact across multiple sectors.
              </p>

              <div className="mt-6 flex space-x-4">
                <Link
                  className="text-gray-400 hover:text-white transition-colors"
                  href="/"
                >
                  <span className="sr-only">Twitter</span>
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </Link>
                <Link
                  className="text-gray-400 hover:text-white transition-colors"
                  href="/"
                >
                  <span className="sr-only">Facebook</span>
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clipRule="evenodd"
                      d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                      fillRule="evenodd"
                    />
                  </svg>
                </Link>
                <Link
                  className="text-gray-400 hover:text-white transition-colors"
                  href="/"
                >
                  <span className="sr-only">Instagram</span>
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      clipRule="evenodd"
                      d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                      fillRule="evenodd"
                    />
                  </svg>
                </Link>
                <Link
                  className="text-gray-400 hover:text-white transition-colors"
                  href="/"
                >
                  <span className="sr-only">LinkedIn</span>
                  <svg
                    className="h-6 w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                  </svg>
                </Link>
              </div>
            </div>
            <div>
              <h3 className="text-white font-bold text-lg mb-4">Quick Links</h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    Organizations
                  </Link>
                </li>
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    Resources
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-bold text-lg mb-4">Resources</h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    Guidelines
                  </Link>
                </li>
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    Registration Process
                  </Link>
                </li>
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    Annual Reports
                  </Link>
                </li>
                <li>
                  <Link
                    className="hover:text-white transition-colors duration-200"
                    href="#"
                  >
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-white font-bold text-lg mb-4">Contact</h3>
              <ul className="space-y-3 text-sm">
                <li className="flex items-center">
                  <svg
                    className="h-5 w-5 mr-2 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                    />
                    <path
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                    />
                  </svg>
                  123 Main Street, Suite 400
                </li>
                <li className="flex items-center">
                  <svg
                    className="h-5 w-5 mr-2 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                    />
                  </svg>
                  <EMAIL>
                </li>
                <li className="flex items-center">
                  <svg
                    className="h-5 w-5 mr-2 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                    />
                  </svg>
                  +1 (555) 123-4567
                </li>
              </ul>
              <div className="mt-6">
                <h4 className="text-white font-medium mb-3">
                  Subscribe to our newsletter
                </h4>
                <form className="flex">
                  <input
                    className="w-full px-3 py-2 text-gray-900 bg-gray-100 rounded-l-lg focus:outline-none"
                    placeholder="Your email"
                    type="email"
                  />
                  <button className="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-r-lg transition-colors">
                    Subscribe
                  </button>
                </form>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-10 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm">&copy; 2025 NGORA. All rights reserved.</p>
            <div className="mt-4 md:mt-0 flex space-x-6">
              <Link
                className="text-sm hover:text-white transition-colors duration-200"
                href="#"
              >
                Privacy Policy
              </Link>
              <Link
                className="text-sm hover:text-white transition-colors duration-200"
                href="#"
              >
                Terms of Service
              </Link>
              <Link
                className="text-sm hover:text-white transition-colors duration-200"
                href="#"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
