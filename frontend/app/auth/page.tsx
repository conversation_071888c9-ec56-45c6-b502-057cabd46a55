"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Checkbox } from "@heroui/checkbox";
import { Image } from "@heroui/image";
import { Input } from "@heroui/input";
import { addToast } from "@heroui/toast";
import Link from "next/link";
import { useEffect, useState } from "react";

import * as AuthService from "@/services/AuthService";
import * as SessionService from "@/services/SessionService";

const LoginPage = () => {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("Admin@123");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      title: "NGORA's Mandate",
      description:
        "The Non-Governmental Organizations Regulatory Authority (NGORA) is a statutory body established by an Act of Parliament. As part of the Ministry of Gender, Community Development and Social Welfare, NGORA is responsible for registering and regulating all NGOs operating in Malawi. Citizens can access NGORA services at district council offices across the country.",
      imagePath: "/api/placeholder/800/600",
    },
    {
      title: "Ensuring Compliance and Accountability",
      description:
        "NGORA enforces annual reporting requirements, mandating NGOs to submit their financial and operational reports within six months of the end of their financial year. By promoting compliance, NGORA ensures transparency and integrity across the non-governmental sector.",
      imagePath: "/api/placeholder/800/600",
    },
    {
      title: "myNGO: A Streamlined Digital Platform",
      description:
        "myNGO is NGORA’s official digital platform designed to simplify key NGO processes. Users can register their organization, renew licenses, apply for TEP processing certificates, and lodge complaints. The system allows for step-by-step form completion with options to save drafts or submit directly, improving accessibility and efficiency.",
      imagePath: "/api/placeholder/800/600",
    },
    {
      title: "NGORA vs. CONGOMA: Distinct Roles",
      description:
        "While NGORA serves as the official regulator overseeing legal compliance of NGOs, CONGOMA acts as a coordinating body that represents and networks NGOs. Understanding the distinction helps stakeholders engage with the appropriate institution for their specific needs.",
      imagePath: "/api/placeholder/800/600",
    },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setIsLoading(true);

    const response = await AuthService.login({
      email,
      password,
      rememberMe,
    });

    if (!response.success) {
      for (const error of response.errors) {
        addToast({
          color: "danger",
          title: "Login Error",
          description: error.message,
        });
      }
    }

    if (response.success && response.data) {
      SessionService.startSession(response.data);
    }

    setIsLoading(false);
  };

  return (
    <div className="flex min-h-screen">
      {/* Left side - Slideshow */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary-600 items-center justify-center p-0 relative overflow-hidden">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              currentSlide === index ? "opacity-100" : "opacity-0"
            }`}
          >
            <div className="absolute inset-0 bg-primary-600 opacity-80 z-10" />
            <div
              className="absolute inset-0 bg-cover bg-center opacity-30 mix-blend-overlay z-0"
              style={{ backgroundImage: `url(${slide.imagePath})` }}
            />
            <div className="absolute inset-0 flex items-center justify-center z-20">
              <div className="max-w-xl text-white p-12">
                <h1 className="text-5xl font-bold mb-8">{slide.title}</h1>
                <p className="text-xl opacity-80 mb-8">{slide.description}</p>

                {/* Testimonial - only show on first slide */}
                {index === 0 && (
                  <div className="bg-white/10 backdrop-blur-sm p-6 rounded-xl mt-12">
                    <p className="italic text-white/90 mb-4">
                      myNGO system has transformed how we monitor and regulate
                      organizations in Malawi. The platform&apos;s efficiency
                      has greatly improved our regulatory processes.
                    </p>
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-primary-400 flex items-center justify-center text-white font-bold">
                        MR
                      </div>
                      <div className="ml-3">
                        <p className="font-medium">Mr. Someone Here</p>
                        <p className="text-sm opacity-80">Board Chairperson</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {/* Slideshow navigation dots */}
        <div className="absolute bottom-6 left-0 right-0 flex justify-center z-30">
          {slides.map((_, index) => (
            <button
              key={index}
              aria-label={`Go to slide ${index + 1}`}
              className={`w-3 h-3 mx-1 rounded-full ${
                currentSlide === index ? "bg-white" : "bg-white/40"
              }`}
              onClick={() => setCurrentSlide(index)}
            />
          ))}
        </div>
      </div>

      {/* Right side - Login form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="text-center mb-10">
            <div className="flex items-center justify-center">
              <Image alt="PMRA Logo" src="/images/logo.png" width={50} />
            </div>
            <h2 className="text-3xl font-bold mt-3">Sign in to myNGO</h2>
            <p className="text-gray-500 mt-2">
              Non-Governmental Regulatory Authority Information System
            </p>
          </div>

          <div className="relative flex items-center my-6">
            <div className="flex-grow border-t border-gray-300 dark:border-gray-700" />
            <span className="flex-shrink mx-4 text-gray-500">
              Sign in with email
            </span>
            <div className="flex-grow border-t border-gray-300 dark:border-gray-700" />
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <Input
                required
                id="email"
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div className="mb-4">
              <Input
                required
                id="password"
                label="Password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div className="flex items-center justify-between mb-6">
              <Checkbox
                defaultSelected
                id="remember"
                isSelected={rememberMe}
                size="md"
                onChange={(e) => setRememberMe(e.target.checked)}
              >
                Remember me
              </Checkbox>
              <Link
                className="text-primary-600 hover:text-primary-800 font-medium text-sm"
                href="/auth/forgot-password"
              >
                Forgot password?
              </Link>
            </div>

            <Button
              className="w-full"
              color="primary"
              disabled={isLoading}
              size="lg"
              type="submit"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      fill="currentColor"
                    />
                  </svg>
                  Processing...
                </span>
              ) : (
                "Sign in"
              )}
            </Button>

            <p className="mt-6 text-center text-gray-600">
              Don&apos;t have an account?{" "}
              <Link
                className="text-primary-600 hover:text-primary-800 font-medium"
                href="/auth/register"
              >
                Sign up
              </Link>
            </p>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
