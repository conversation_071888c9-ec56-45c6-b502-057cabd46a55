"use client";

import { createContext, useContext, useEffect, useState } from "react";

type ThemeType = "light" | "dark";

type ThemeContextType = {
	theme: ThemeType;
	setTheme: (theme: ThemeType) => void;
};

// Create context with a default value
const ThemeContext = createContext<ThemeContextType>({
	theme: "light",
	setTheme: () => null,
});

// Custom hook to use the theme context
export function useTheme() {
	const context = useContext(ThemeContext);
	if (context === undefined) {
		throw new Error("useTheme must be used within a ThemeProvider");
	}
	return context;
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
	// Initialize with default theme - will be updated in useEffect
	const [theme, setThemeState] = useState<ThemeType>("light");

	// Function to set theme that also updates localStorage
	const setTheme = (newTheme: ThemeType) => {
		setThemeState(newTheme);
		
		// Only access localStorage in browser environment
		if (typeof window !== "undefined") {
			localStorage.setItem("heroui-theme", newTheme);
		}
	};

	// Load saved theme on initial mount
	useEffect(() => {
		// Only run in browser environment
		if (typeof window !== "undefined") {
			// Get theme from localStorage or use system preference
			let savedTheme: ThemeType = "light";
			
			// Try to get from localStorage first
			const storedTheme = localStorage.getItem("heroui-theme");
			if (storedTheme === "dark" || storedTheme === "light") {
				savedTheme = storedTheme;
			} else {
				// Fall back to system preference
				savedTheme = window.matchMedia("(prefers-color-scheme: dark)").matches 
					? "dark" 
					: "light";
				// Save this preference
				localStorage.setItem("heroui-theme", savedTheme);
			}
			
			setThemeState(savedTheme);
		}
	}, []);

	return (
		<ThemeContext.Provider value={{ theme, setTheme }}>
			<div className={theme === "dark" ? "dark" : ""}>
				{children}
			</div>
		</ThemeContext.Provider>
	);
}
