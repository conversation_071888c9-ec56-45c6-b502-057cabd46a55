{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev -p 4000 --turbopack", "build": "next build", "start": "next start", "lint": "eslint --fix"}, "dependencies": {"@heroui/accordion": "^2.2.16", "@heroui/alert": "^2.2.19", "@heroui/autocomplete": "^2.3.20", "@heroui/avatar": "^2.2.15", "@heroui/badge": "^2.2.12", "@heroui/breadcrumbs": "^2.2.15", "@heroui/button": "2.2.19", "@heroui/calendar": "^2.2.19", "@heroui/card": "^2.2.18", "@heroui/checkbox": "^2.3.18", "@heroui/chip": "^2.2.15", "@heroui/code": "2.2.14", "@heroui/date-input": "^2.3.18", "@heroui/date-picker": "^2.3.19", "@heroui/drawer": "^2.2.16", "@heroui/dropdown": "^2.3.19", "@heroui/form": "^2.1.18", "@heroui/image": "^2.2.12", "@heroui/input": "2.4.19", "@heroui/input-otp": "^2.1.18", "@heroui/kbd": "2.2.15", "@heroui/link": "2.2.16", "@heroui/listbox": "2.3.18", "@heroui/menu": "^2.2.18", "@heroui/modal": "^2.2.16", "@heroui/navbar": "2.2.17", "@heroui/number-input": "^2.0.9", "@heroui/pagination": "^2.2.17", "@heroui/popover": "^2.3.19", "@heroui/progress": "^2.2.15", "@heroui/radio": "^2.3.18", "@heroui/ripple": "^2.2.14", "@heroui/select": "^2.4.19", "@heroui/skeleton": "^2.2.12", "@heroui/slider": "^2.4.16", "@heroui/snippet": "2.2.20", "@heroui/spinner": "^2.2.16", "@heroui/switch": "2.2.17", "@heroui/system": "2.4.15", "@heroui/table": "^2.2.18", "@heroui/tabs": "^2.2.16", "@heroui/theme": "2.4.15", "@heroui/toast": "^2.0.9", "@heroui/tooltip": "^2.2.16", "@heroui/use-theme": "^2.1.8", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.22", "axios": "^1.9.0", "clsx": "2.1.1", "framer-motion": "11.13.1", "intl-messageformat": "10.7.16", "lucide-react": "^0.509.0", "next": "15.3.1", "next-themes": "0.4.6", "react": "18.3.1", "react-dom": "18.3.1", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@next/eslint-plugin-next": "15.3.1", "@react-types/shared": "3.29.0", "@types/node": "22.15.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "autoprefixer": "10.4.21", "eslint": "9.25.1", "eslint-config-next": "15.3.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "postcss": "8.5.3", "prettier": "3.5.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}