"use client";

import { usePathname, useRouter } from "next/navigation";
import { ReactNode, useEffect } from "react";

import { useStore } from "@/composables/useStore";
import { getAccessToken, getSession } from "@/services/SessionService";

interface Props {
  node: ReactNode;
}

const ProtectedRoute = ({ node }: Props): ReactNode => {
  const { setUser } = useStore();
  const router = useRouter();
  const currentPath = usePathname();

  const isAuthenticated = () => {
    const token = getAccessToken();

    return !!token;
  };

  useEffect(() => {
    if (isAuthenticated()) {
      const currentSession = getSession();

      setUser(currentSession);
    }
  }, [currentPath, setUser]);

  if (!isAuthenticated()) {
    router.push("/auth/login");

    return null as ReactNode;
  }

  return node;
};

export default ProtectedRoute;
