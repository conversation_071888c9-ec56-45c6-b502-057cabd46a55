import { AxiosResponse } from "axios";

import { HttpResponse } from "@/types";

export function httpResponse<T>(response: AxiosResponse): HttpResponse<T> {
  if (!response) {
    return {
      data: undefined,
      success: false,
      errors: [],
      status: 404,
      total: 0,
      page: 1,
      size: 10,
    };
  }

  if (Object.keys(response.data).includes("detail")) {
    return {
      data: response.data.detail.data,
      success: response.data.detail.success,
      errors: response.data.detail.errors,
      status: response.status,
      total: response.data.detail.total,
      page: response.data.detail.page,
      size: response.data.detail.size,
    };
  }

  return {
    data: response.data.data,
    success: response.data.success,
    errors: response.data.errors,
    status: response.status,
    total: response.data.total,
    page: response.data.page,
    size: response.data.size,
  };
}

export function jsonToQueryParams(json: Record<string, any>): string {
  return Object.keys(json)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`)
    .join("&");
}

export function getTotalPages(
  totalCount: number,
  itemsPerPage: number
): number {
  if (itemsPerPage <= 0) {
    throw new Error("itemsPerPage must be greater than 0");
  }

  return Math.ceil(totalCount / itemsPerPage);
}
