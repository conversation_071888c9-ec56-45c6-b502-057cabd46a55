import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import { siteConfig } from "@/config/site";
import { ISession } from "@/types";

interface AppState {
  session: ISession | null;
  isAuthenticated: boolean;

  sidebarExpanded: boolean;
  darkMode: boolean;
  currentPage: string;

  setUser: (session: ISession | null) => void;
  destroySession: () => void;
  toggleSidebar: () => void;
  setSidebarExpanded: (expanded: boolean) => void;
  toggleDarkMode: () => void;
  setCurrentPage: (page: string) => void;
}

export const useStore = create<AppState>()(
  persist(
    (set) => ({
      session: null,
      isAuthenticated: false,
      sidebarExpanded: true,
      darkMode: false,
      currentPage: "",

      setUser: (session) => set({ session, isAuthenticated: !!session }),

      destroySession: () =>
        set({
          session: null,
          isAuthenticated: false,
        }),

      toggleSidebar: () =>
        set((state) => ({
          sidebarExpanded: !state.sidebarExpanded,
        })),

      setSidebarExpanded: (expanded) =>
        set({
          sidebarExpanded: expanded,
        }),

      toggleDarkMode: () =>
        set((state) => ({
          darkMode: !state.darkMode,
        })),

      setCurrentPage: (page) =>
        set(
          {
            currentPage: page,
          },
          false
        ),
    }),
    {
      name: siteConfig.APP_STATE,
      storage: createJSONStorage(() => localStorage),
    }
  )
);

export const useAuth = () => {
  const { session, isAuthenticated, destroySession } = useStore();

  return { session, isAuthenticated, destroySession };
};

export const useUI = () => {
  const {
    sidebarExpanded,
    currentPage,
    darkMode,
    toggleSidebar,
    setSidebarExpanded,
    toggleDarkMode,
    setCurrentPage,
  } = useStore();

  return {
    sidebarExpanded,
    currentPage,
    darkMode,
    toggleSidebar,
    setSidebarExpanded,
    toggleDarkMode,
    setCurrentPage,
  };
};
