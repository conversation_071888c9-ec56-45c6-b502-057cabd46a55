# Ngora Monorepo

Welcome, Team Tumbati! This monorepo houses both our backend and frontend codebases. Please follow these guidelines to keep our collaboration smooth and effective.

---

## 🚫 **TEAM RULE: NO DIRECT PUSHES TO MAIN**

No one should commit or push directly to the `main` branch.  
All changes must go through feature branches and Pull Requests (PRs) with reviews from fellow teammates.

---

## Team Workflow for Contributions

### 1. Branch from Main

Always start by branching off the latest `main`:

```bash
git checkout main
git pull origin main
git checkout -b <feature|bugfix|chore>/<short-description>
```

Example:  
`git checkout -b feature/user-authentication`  
`git checkout -b bugfix/fix-login-error`

### 2. Work on Your Task

- Implement your feature or bugfix in your branch.
- Add or update tests as needed.
- Ensure all tests and linters pass before pushing.

### 3. Commit Your Changes

- Use clear, descriptive commit messages.
- Commit regularly and logically.

### 4. Push to Remote

```bash
git push origin <your-branch-name>
```

### 5. Open a Pull Request (PR)

You can open a PR either via the GitHub web UI or using the GitHub CLI:

#### On GitHub.com

- Visit the repo: [https://github.com/tumbati/ngora-monorepo](https://github.com/tumbati/ngora-monorepo)
- Click "Compare & pull request" or "New pull request".
- Set your branch as the source and `main` as the target.

#### Using GitHub CLI

If you have the [GitHub CLI](https://cli.github.com/) installed, you can create a PR from your terminal:

```bash
gh pr create --base main --head <your-branch-name> --fill
```

- The `--fill` flag will use your commit messages for the PR title and description.  
- You can also add `--reviewer <github-username>` and `--label <label>` options as needed.

#### PR Guidelines

In your PR description, include:

- The problem solved or feature added
- Any breaking changes
- How it was tested
- References to related issues (e.g., "Closes #12")

Assign teammates as reviewers and add relevant labels.

### 6. Review & Feedback

- Engage in code review—respond to suggestions and make updates.
- Respect each other's perspectives and keep feedback constructive.

### 7. Merging

- Only maintainers merge PRs into `main` after approvals.
- PRs will be squashed or merged based on our repository settings.

---

## 📚 Project Documentation

- **Backend README:** [backend/README.md](backend/README.md)
- **Frontend README:** [frontend/README.md](frontend/README.md)

---

## Code of Conduct

We're committed to a respectful and inclusive environment. Please follow the [Contributor Covenant](https://www.contributor-covenant.org/) Code of Conduct.

---

## Need Help?

Questions or suggestions? Open a discussion or reach out to maintainers via GitHub Issues or Discussions.

Let's keep building together!
